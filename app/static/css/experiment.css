/* ./app/static/css/experiment.css */
/* 统一的实验样式文件 */

/* Vue导航组件样式 */
.substep-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 15px 0;
}

.substep-nav button, .nav-button {
    display: inline-block;
    margin: 15px 5px;
    padding: 12px 15px;
    background-color: #3498db;
    color: white !important;
    border: none;
    border-radius: 5px;
    font-size: 16px !important;
    cursor: pointer;
    transition: all 0.3s ease;
    width: auto;
    min-width: 100px;
    font-family: 'Noto Sans SC', 'Microsoft YaHei', 'SimHei', 'Segoe UI', Arial, sans-serif !important;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-weight: 400;
    line-height: 1.4;
}

.substep-nav button:hover, .nav-button:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.substep-nav button.prev-btn, .nav-button.prev-btn {
    background-color: #95a5a6;
}

.substep-nav button.prev-btn:hover, .nav-button.prev-btn:hover {
    background-color: #7f8c8d;
}

.validation-btn {
    background-color: #17a2b8 !important;
    color: white !important;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px !important;
    font-family: 'Noto Sans SC', 'Microsoft YaHei', 'SimHei', 'Segoe UI', Arial, sans-serif !important;
    font-weight: 400;
    line-height: 1.4;
}

.validation-btn:hover {
    background-color: #138496 !important;
}

.substep-nav button:disabled, .nav-button:disabled {
    background-color: #6c757d !important;
    cursor: not-allowed;
    color: white !important;
}

.substep-nav button:disabled:hover, .nav-button:disabled:hover {
    background-color: #6c757d !important;
    transform: none;
    box-shadow: none;
}

* {
    box-sizing: border-box;
    font-family: 'Segoe UI', Arial, sans-serif;
}

body {
    background-color: #f5f7fa;
    margin: 0;
    padding: 10px;
    color: #333;
    font-size: 16px;
}

.container {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

h1, h2, h3, h4 {
    color: #2c3e50;
}

h1 {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 3px solid #3498db;
    font-size: 24px;
}

h2 {
    margin-top: 30px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
    font-size: 22px;
}

.step-container {
    margin: 20px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    display: none;
}

.step-container.active {
    display: block;
    background-color: #fff;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    animation: fadeIn 0.5s ease;
}

.step-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: #3498db;
    color: white;
    border-radius: 50%;
    font-weight: bold;
    margin-right: 12px;
    font-size: 18px;
}

.step-title {
    font-size: 20px;
    font-weight: 550;
    color: #2c3e50;
}

/* 子步骤样式 */
.substep-container {
    margin: 15px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    display: none;
}

.substep-container.active {
    display: block;
    border-left: 3px solid #3498db;
    animation: fadeIn 0.5s ease;
}

.substep-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.substep-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background-color: #3498db;
    color: white;
    border-radius: 50%;
    font-weight: bold;
    margin-right: 10px;
    font-size: 16px;
}

.substep-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.substep-content {
    margin-left: 15px;
    font-size: 16px;
}

.progress-bar {
    height: 8px;
    background-color: #ecf0f1;
    border-radius: 4px;
    margin: 15px 0;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: #3498db;
    transition: width 0.3s ease;
}

/* 消息框样式 */
.instruction {
    background-color: #e8f4fc;
    padding: 15px;
    border-left: 4px solid #3498db;
    margin: 15px 0;
    border-radius: 4px;
    font-size: 16px;
}

.warning {
    background-color: #fff3cd;
    padding: 15px;
    border-left: 4px solid #ffc107;
    margin: 15px 0;
    border-radius: 4px;
    font-size: 16px;
}

.danger {
    background-color: #f8d7da;
    padding: 15px;
    border-left: 4px solid #dc3545;
    margin: 15px 0;
    border-radius: 4px;
    font-size: 16px;
}

.success-message {
    background-color: #d4edda;
    padding: 15px;
    border-left: 4px solid #28a745;
    margin: 15px 0;
    border-radius: 4px;
    font-size: 16px;
}

.highlight {
    background-color: #fffde7;
    padding: 5px;
    border-radius: 3px;
    font-weight: bold;
    color: #ff6f00;
    border: 1px dashed #ffc107;
    font-size: 16px;
}

/* 按钮样式 */
button {
    display: inline-block;
    margin: 15px 5px;
    padding: 12px 15px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: auto;
    min-width: 100px;
}

button:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

button:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

button.prev-btn {
    background-color: #95a5a6;
}

button.prev-btn:hover {
    background-color: #7f8c8d;
}

.btn-container {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    flex-wrap: wrap;
}

.substep-nav {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    align-items: center;
}

/* 表格样式 */
.table-container {
    overflow-x: auto;
    margin: 20px 0;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    background-color: #fff;
}

th, td {
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    text-align: center;
    vertical-align: middle;
}

thead th {
    background-color: #3498db;
    color: white;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 1;
}

tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

tbody tr:hover {
    background-color: #f1f1f1;
}

/* Specific table types */
.measurement-table {
    margin: 20px auto;
    max-width: 95%;
}

.horizontal-table {
    min-width: 800px; /* Kept from original */
}

.lissajous-table {
    max-width: 500px; /* Kept from original */
    margin: 20px auto;
}

/* ================== Input Styles ================== */
/* General input styling */
input[type="text"],
input[type="number"],
input[type="email"],
input[type="password"] {
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s, box-shadow 0.3s;
    box-sizing: border-box;
}

input[type="text"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
input[type="password"]:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.25);
}

/* For measurement inputs in tables or specific sections */
.measurement-table input,
.horizontal-table input,
.data-input-section input,
.student-input input,
td input[type="number"] {
    width: 90px;
    text-align: center;
    padding: 6px;
    border: 1px solid #ddd;
    background-color: #f8f9fa;
    border-radius: 4px;
    -moz-appearance: textfield; /* Firefox */
}

.measurement-table input:focus,
.horizontal-table input:focus,
.data-input-section input:focus,
.student-input input:focus,
td input[type="number"]:focus {
     background-color: #fff;
     border-color: #007bff;
     box-shadow: none; /* No shadow for table inputs to keep it clean */
}

/* Hide number input spinners for all browsers */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type=number] {
    -moz-appearance: textfield;
}

/* 全屏表格样式 */
.fullscreen-table {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.95);
    z-index: 1000;
    display: none;
    overflow: auto;
    padding: 20px;
}

.fullscreen-table-content {
    max-width: 1000px;
    margin: 20px auto;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
}

.close-table-btn {
    background-color: #e74c3c;
}

.close-table-btn:hover {
    background-color: #c0392b;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.modal-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-body {
    margin: 15px 0;
}

.modal-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

/* 输入组样式 */
.input-group {
    margin: 15px 0;
    width: 100%;
}

.input-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.input-group input {
    width: 100%;
    max-width: none;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    box-sizing: border-box;
}

/* 表单选择器样式 */
.form-select {
    width: 100%;
    max-width: 300px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

.form-text {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

/* 加载动画样式 */
.loading {
    display: none;
    text-align: center;
    margin: 20px 0;
}

.loading-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #3498db;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 10px;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 3000;
}

.loading-content {
    text-align: center;
}

/* 分析结果样式 */
.analysis-container {
    margin: 30px 0;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #27ae60;
}

.analysis-content {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #27ae60;
    line-height: 1.6;
}

.analysis-result {
    margin: 20px 0;
    padding: 15px;
    border-radius: 8px;
    display: none;
}

.analysis-result.success {
    background-color: #d4edda;
    border-left: 4px solid #28a745;
}

.analysis-result.error {
    background-color: #f8d7da;
    border-left: 4px solid #dc3545;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.footer {
    text-align: center;
    color: #666;
    font-size: 14px;
    margin-top: 30px;
    padding-top: 10px;
    border-top: 1px solid #ddd;
}

/* 响应式设计 */
@media (max-width: 768px) {
    body {
        padding: 5px;
        font-size: 16px;
    }

    .container {
        padding: 15px;
    }

    h1 {
        font-size: 22px;
    }

    .step-container {
        padding: 12px;
    }

    .step-number {
        width: 32px;
        height: 32px;
        font-size: 16px;
    }

    .step-title {
        font-size: 18px;
    }

    button {
        padding: 10px 12px;
        margin: 10px 3px;
        min-width: 80px;
        font-size: 15px;
    }

    input {
        max-width: 60px;
        padding: 8px;
    }

    th, td {
        padding: 8px 4px;
        font-size: 14px;
    }

    .fullscreen-table-content {
        margin: 10px;
        padding: 15px;
    }
}
.data-table-compact {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
    font-size: 0.9em;
}

.data-table-compact th,
.data-table-compact td {
    border: 1px solid #ddd;
    padding: 6px 8px;
    text-align: left;
}

.data-table-compact th {
    background-color: #f2f2f2;
    font-weight: bold;
}

.data-table-compact tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

/* ===== 实验通用样式 ===== */

/* 不确定度计算容器 */
.uncertainty-container {
    background-color: #f8f9fa;
    border: 2px solid #007bff;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

/* 公式显示框 */
.formula-box {
    background-color: #e9ecef;
    border-left: 4px solid #007bff;
    padding: 15px;
    margin: 15px 0;
    font-family: 'Courier New', monospace;
}

/* 计算步骤 */
.calculation-step {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 5px;
    padding: 15px;
    margin: 10px 0;
}

/* 学生输入区域 */
.student-input {
    background-color: #e8f5e8;
    border: 2px solid #28a745;
    border-radius: 5px;
    padding: 10px;
    margin: 10px 0;
}

/* 数据显示区域 */
.data-display {
    background-color: #e3f2fd;
    border: 2px solid #2196f3;
    border-radius: 5px;
    padding: 15px;
    margin: 15px 0;
}

/* 结果对比 */
.result-comparison {
    display: flex;
    justify-content: space-between;
    margin: 15px 0;
}

.result-box {
    flex: 1;
    margin: 0 10px;
    padding: 15px;
    border-radius: 5px;
}

.student-result {
    background-color: #e8f5e8;
    border: 2px solid #28a745;
}

.correct-result {
    background-color: #e3f2fd;
    border: 2px solid #2196f3;
}

.check-result {
    margin: 15px 0;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #007bff;
}

/* ===== 计算器样式 ===== */

/* 计算器控制区域 */
.calculator-control {
    text-align: center;
    margin: 15px 0;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    width: 100%;
}

.calculator-toggle-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    border-radius: 20px;
    padding: 10px 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.calculator-toggle-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.calculator-toggle-btn:active {
    transform: translateY(0);
}

/* 计算器容器 */
.calculator-container {
    margin: 20px auto;
    max-width: 400px;
    display: none;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-15px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 简单计算器 */
.simple-calculator {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid #e0e0e0;
    width: 100%;
    box-sizing: border-box;
}

.calc-display {
    display: block;
    width: 100%;
    height: 70px;
    font-size: 28px;
    text-align: right;
    padding: 0 20px;
    border: 2px solid #007bff;
    border-radius: 8px;
    margin-bottom: 20px;
    background-color: #f8f9fa;
    font-family: 'Courier New', monospace;
    box-sizing: border-box;
}

.calc-buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
}

.calc-btn {
    height: 50px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.calc-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

.calc-btn:active {
    transform: translateY(0);
}

/* 计算器按钮颜色样式 */
.calc-btn.number {
    background-color: #e9ecef;
    color: #333;
}

.calc-btn.operator {
    background-color: #007bff;
    color: white;
}

.calc-btn.function {
    background-color: #6c757d;
    color: white;
}

.calc-btn.clear {
    background-color: #dc3545;
    color: white;
}

.calc-btn.equals {
    background-color: #28a745;
    color: white;
}

/* ===== 自由落体实验样式 ===== */

/* 公式显示 */
.formula-display {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.formula-display h3 {
    margin: 0 0 15px 0;
    font-size: 20px;
}

.formula-display .katex {
    font-size: 20px;
}

/* 测量设置 */
.measurement-setup {
    background: #f8f9fa;
    border: 2px solid #007bff;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

/* 数据输入区域 */
.data-input-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 计算结果 */
.calculation-result {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: 2px solid #28a745;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    text-align: center;
}

.gravity-result {
    font-size: 24px;
    font-weight: bold;
    color: #155724;
    margin: 10px 0;
}

/* 误差分析 */
.error-analysis {
    background: #fff3cd;
    border: 2px solid #ffc107;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

/* 图表容器 */
.plot-container {
    text-align: center;
    margin: 30px 0;
}

.plot-image {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* 光电计时器相关样式 */
.equipment-setup {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 2px solid #2196f3;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

.equipment-setup h3 {
    color: #1565c0;
    margin-bottom: 15px;
}

.setup-checklist {
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    border-left: 4px solid #2196f3;
}

.setup-checklist ul {
    margin: 0;
    padding-left: 20px;
}

.setup-checklist li {
    margin: 8px 0;
    color: #333;
}

.photogate-info {
    background: #f3e5f5;
    border: 2px solid #9c27b0;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.photogate-info h4 {
    color: #7b1fa2;
    margin-bottom: 10px;
}

.measurement-tips {
    background: #fff8e1;
    border: 2px solid #ff9800;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.measurement-tips h4 {
    color: #f57c00;
    margin-bottom: 10px;
}

/* ===== 示波器实验样式 ===== */

/* 全屏表格容器样式 */
.fullscreen-table {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    display: none;
    overflow-y: auto;
}

.fullscreen-table-content {
    background-color: white;
    margin: 20px;
    padding: 20px;
    border-radius: 8px;
    max-width: 1200px;
    margin: 20px auto;
}

/* ===== 制流电路实验样式 ===== */

/* 电路图 */
.circuit-diagram {
    text-align: center;
    margin: 20px 0;
}

.circuit-diagram img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    border: 1px solid #ddd;
}

#plot-container {
    margin-top: 30px;
    text-align: center;
}

#plot-image {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ===== 分光计实验样式 ===== */

/* 仪器图 */
.instrument-diagram {
    margin: 20px 0;
    text-align: center;
}

.component-list {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.component-list ul {
    margin: 0;
    padding-left: 20px;
}

.component-list li {
    margin: 8px 0;
}

.reading-method {
    background: #e3f2fd;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.step-guide {
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    border-left: 4px solid #007bff;
}

.visual-guide {
    margin: 20px 0;
}

.vision-comparison {
    display: flex;
    justify-content: space-around;
    margin: 15px 0;
}

.vision-item {
    text-align: center;
    flex: 1;
    margin: 0 10px;
}

.parallel-light-theory {
    background: #fff3cd;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    border-left: 4px solid #ffc107;
}

.precision-note {
    background: #f8d7da;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    border-left: 4px solid #dc3545;
}

.completion-check {
    background: #d4edda;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    border-left: 4px solid #28a745;
}

/* ===== 密度测量实验样式 ===== */

/* 输入框样式优化 */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type=number] {
    -moz-appearance: textfield;
}

/* ===== 响应式设计补充 ===== */

@media (max-width: 768px) {
    .result-comparison {
        flex-direction: column;
    }
    
    .result-box {
        margin: 10px 0;
    }
    
    .vision-comparison {
        flex-direction: column;
    }
    
    .vision-item {
        margin: 10px 0;
    }
    
    .calculator-container {
        max-width: 100%;
        margin: 10px;
    }
    
    .simple-calculator {
        padding: 15px;
    }
    
    .calc-buttons {
        gap: 8px;
    }
    
    .calc-btn {
        height: 45px;
        font-size: 14px;
    }
    
    .fullscreen-table-content {
        margin: 10px;
        padding: 15px;
    }
}
