// ./app/static/js/vue-components.js
// 通用的Vue.js组件

// 通用导航组件
const ExperimentNavigationComponent = {
    props: {
        currentStep: {
            type: Number,
            required: true
        },
        currentSubstep: {
            type: Number,
            default: 1
        },
        totalSubsteps: {
            type: Number,
            default: 1
        },
        showPrev: {
            type: Boolean,
            default: true
        },
        showNext: {
            type: Boolean,
            default: true
        },
        nextText: {
            type: String,
            default: '下一步'
        },
        prevText: {
            type: String,
            default: '上一步'
        },
        isFinalSubstep: {
            type: Boolean,
            default: false
        },
        isStepStart: {
            type: Boolean,
            default: false
        },
        hasValidation: {
            type: Boolean,
            default: false
        },
        validationFunction: {
            type: String,
            default: ''
        },
        nextButtonId: {
            type: String,
            default: ''
        }
    },
    template: `
        <div class="substep-nav">
            <button v-if="showPrev" @click="handlePrev" :class="prevButtonClass" class="nav-button">
                {{ prevText || '上一步' }}
            </button>
            <div v-else></div>

            <div v-if="hasValidation" style="display: flex; gap: 10px;">
                <button @click="handleValidation" class="validation-btn nav-button">
                    验证数据
                </button>
                <button
                    v-if="showNext"
                    @click="handleNext"
                    :disabled="isNextDisabled"
                    :id="nextButtonId"
                    :class="nextButtonClass"
                    class="nav-button">
                    {{ nextText || '下一步' }}
                </button>
            </div>
            <button
                v-else-if="showNext"
                @click="handleNext"
                :class="nextButtonClass"
                class="nav-button">
                {{ nextText || '下一步' }}
            </button>
            <div v-else></div>
        </div>
    `,
    computed: {
        prevButtonClass() {
            return this.isStepStart ? 'prev-btn' : '';
        },
        nextButtonClass() {
            return '';
        },
        isNextDisabled() {
            return this.hasValidation && this.nextButtonId;
        }
    },
    methods: {
        handlePrev() {
            if (this.isStepStart) {
                this.$emit('prev', this.currentStep);
            } else {
                this.$emit('prev', `step${this.currentStep}`, this.currentSubstep, this.totalSubsteps);
            }
        },
        handleNext() {
            console.log('Navigation handleNext called', {
                isFinalSubstep: this.isFinalSubstep,
                isStepStart: this.isStepStart,
                currentStep: this.currentStep,
                currentSubstep: this.currentSubstep,
                nextText: this.nextText
            });

            if (this.isFinalSubstep || this.isStepStart) {
                this.$emit('next', this.currentStep);
            } else {
                this.$emit('next', `step${this.currentStep}`, this.currentSubstep, this.totalSubsteps);
            }
        },
        handleValidation() {
            this.$emit('validate', this.validationFunction);
        }
    }
};

// 通用进度条组件
const ProgressBarComponent = {
    props: {
        stepId: {
            type: String,
            required: true
        },
        currentSubstep: {
            type: Number,
            default: 1
        },
        totalSubsteps: {
            type: Number,
            default: 1
        }
    },
    template: `
        <div class="progress-bar">
            <div class="progress-fill" :id="stepId + '-progress'" :style="progressStyle"></div>
        </div>
    `,
    computed: {
        progressStyle() {
            const percentage = (this.currentSubstep / this.totalSubsteps) * 100;
            return { width: `${percentage}%` };
        }
    }
};

// 通用数据输入表格组件
const DataInputTableComponent = {
    props: {
        title: {
            type: String,
            required: true
        },
        unit: {
            type: String,
            required: true
        },
        inputClass: {
            type: String,
            required: true
        },
        rows: {
            type: Number,
            default: 5
        }
    },
    template: `
        <div>
            <h4>{{ title }} ({{ unit }}) 数据记录</h4>
            <div class="measurement-table">
                <table>
                    <thead>
                        <tr>
                            <th>测量次数</th>
                            <th>{{ title }} ({{ unit }})</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="i in rows" :key="i">
                            <td>{{ i }}</td>
                            <td>
                                <input 
                                    type="number" 
                                    step="0.01" 
                                    :class="inputClass" 
                                    required>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    `
};

// 通用计算输入组件
const CalculationInputComponent = {
    props: {
        label: {
            type: String,
            required: true
        },
        inputId: {
            type: String,
            required: true
        },
        unit: {
            type: String,
            default: ''
        },
        step: {
            type: String,
            default: '0.001'
        },
        placeholder: {
            type: String,
            default: '输入计算结果'
        },
        calculatorId: {
            type: String,
            default: ''
        },
        resultId: {
            type: String,
            default: ''
        }
    },
    template: `
        <div class="uncertainty-container">
            <div class="student-input">
                <label>{{ label }} {{ unit ? '(' + unit + ')' : '' }}：</label>
                <input 
                    type="number" 
                    :step="step" 
                    :id="inputId" 
                    :placeholder="placeholder">
            </div>
            <div v-if="calculatorId" class="calculator-control">
                <button 
                    class="calculator-toggle-btn" 
                    @click="toggleCalculator(calculatorId)">
                    🧮 使用计算器辅助计算
                </button>
            </div>
            <div 
                v-if="calculatorId" 
                :id="calculatorId" 
                class="calculator-container" 
                style="display: none; margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 10px; border: 1px solid #e9ecef;">
            </div>
            <div 
                v-if="resultId" 
                :id="resultId" 
                class="check-result" 
                style="display: none;">
            </div>
        </div>
    `,
    methods: {
        toggleCalculator(calculatorId) {
            // 调用全局的toggleCalculator函数
            if (window.toggleCalculator) {
                window.toggleCalculator(calculatorId);
            }
        }
    }
};

// 通用实验提交对话框组件
const ExperimentSubmissionModalComponent = {
    props: {
        experimentType: {
            type: String,
            required: true
        },
        modalId: {
            type: String,
            default: 'experiment-submission-modal'
        },
        visible: {
            type: Boolean,
            default: false
        }
    },
    template: `
        <div v-if="visible" :id="modalId" class="modal" style="display: flex !important; position: fixed; left: 0; top: 0; width: 100vw; height: 100vh; background: rgba(0,0,0,0.5); align-items: center; justify-content: center; z-index: 99999;">
            <div style="background: #fff; border-radius: 10px; padding: 30px; min-width: 320px; max-width: 90vw; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
                <h3>提交实验结果</h3>
                <div class="input-group" style="margin: 15px 0;">
                    <label for="student-id" style="display: block; margin-bottom: 5px;">学号：</label>
                    <input type="text" id="student-id" v-model="studentId" placeholder="请输入学号" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                <div class="input-group" style="margin: 15px 0;">
                    <label for="student-name" style="display: block; margin-bottom: 5px;">姓名：</label>
                    <input type="text" id="student-name" v-model="studentName" placeholder="请输入姓名" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                <div v-if="submissionStatus.show" :id="modalId + '-status'"
                     :style="{ display: 'block', marginTop: '10px', padding: '10px', borderRadius: '5px', backgroundColor: submissionStatus.bgColor, color: submissionStatus.textColor }">
                    {{ submissionStatus.message }}
                </div>
                <div style="margin-top: 20px; text-align: right;">
                    <button @click="handleSubmit" :disabled="isSubmitting" style="padding: 10px 20px; margin-right: 10px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        {{ isSubmitting ? '提交中...' : '提交' }}
                    </button>
                    <button @click="handleCancel" :disabled="isSubmitting" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">取消</button>
                </div>
            </div>
        </div>
    `,
    data() {
        return {
            studentId: '',
            studentName: '',
            isSubmitting: false,
            submissionStatus: {
                show: false,
                message: '',
                bgColor: '#e8f4fc',
                textColor: '#333'
            }
        };
    },
    watch: {
        visible(newVal) {
            console.log('Submission modal visibility changed:', newVal, 'for experiment:', this.experimentType);
            if (newVal) {
                console.log('Modal should be visible now');
                // 检查DOM元素是否存在
                this.$nextTick(() => {
                    const modalElement = document.getElementById(this.modalId);
                    console.log('Modal DOM element:', modalElement);
                    if (modalElement) {
                        console.log('Modal element styles:', window.getComputedStyle(modalElement));
                    }
                });
            } else {
                console.log('Modal should be hidden now');
            }
        }
    },
    mounted() {
        console.log('ExperimentSubmissionModalComponent mounted for:', this.experimentType);
        console.log('Initial visible state:', this.visible);
    },
    methods: {
        handleSubmit() {
            if (!this.studentId || !this.studentName) {
                this.showStatus('学号和姓名不能为空！', '#f8d7da', '#721c24');
                return;
            }

            this.isSubmitting = true;
            this.showStatus('正在提交...', '#e8f4fc', '#333');

            this.$emit('submit', {
                studentId: this.studentId,
                studentName: this.studentName,
                experimentType: this.experimentType
            });
        },
        handleCancel() {
            this.$emit('cancel');
        },
        showStatus(message, bgColor = '#e8f4fc', textColor = '#333') {
            this.submissionStatus = {
                show: true,
                message,
                bgColor,
                textColor
            };
        },
        showSuccess(message) {
            this.isSubmitting = false;
            this.showStatus(message, '#d4edda', '#155724');

            // 3秒后自动关闭
            setTimeout(() => {
                this.$emit('success');
            }, 3000);
        },
        showError(message) {
            this.isSubmitting = false;
            this.showStatus(message, '#f8d7da', '#721c24');
        },
        reset() {
            this.studentId = '';
            this.studentName = '';
            this.isSubmitting = false;
            this.submissionStatus.show = false;
        }
    },
    watch: {
        visible(newVal) {
            if (!newVal) {
                this.reset();
            }
        }
    }
};

// 通用实验工具函数
const ExperimentUtils = {
    // AI分析函数
    analyzeWithAI: function(endpoint, data, modelId, successCallback, errorCallback) {
        // 获取选中的模型ID
        const selectedModelId = modelId || document.getElementById('model-select')?.value || '';

        // 显示加载状态
        const loadingElement = document.getElementById('analysis-loading');
        if (loadingElement) {
            loadingElement.style.display = 'block';
        }

        // 准备请求数据
        const requestData = {
            ...data,
            model_id: selectedModelId
        };

        // 发送POST请求
        fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(result => {
            // 隐藏加载状态
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }

            if (result.error) {
                throw new Error(result.error);
            }

            // 调用成功回调
            if (successCallback) {
                successCallback(result.analysis || result.check_result || result);
            }
        })
        .catch(error => {
            // 隐藏加载状态
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }

            console.error('AI分析错误:', error);

            // 调用错误回调
            if (errorCallback) {
                errorCallback(error.message || '分析失败');
            }
        });
    },

    // 显示分析结果
    displayAnalysisResult: function(containerId, contentId, content) {
        const container = document.getElementById(containerId);
        const contentElement = document.getElementById(contentId);

        if (container && contentElement) {
            container.style.display = 'block';
            contentElement.innerHTML = content;
        }
    },

    // 数据验证函数
    validateMeasurementData: function(inputClass, dataName, nextButtonId, displayId) {
        const inputs = document.getElementsByClassName(inputClass);
        const data = [];
        let isValid = true;
        let errorMessage = '';

        // 检查输入是否存在
        if (inputs.length === 0) {
            console.warn(`No inputs found with class: ${inputClass}`);
            return null;
        }

        // 检查所有输入
        for (let i = 0; i < inputs.length; i++) {
            const value = parseFloat(inputs[i].value);
            if (isNaN(value) || value <= 0) {
                isValid = false;
                errorMessage = `请填写所有${dataName}数据，且数据必须为正数！`;
                break;
            }
            data.push(value);
        }

        // 显示验证结果
        const displayElement = document.getElementById(displayId);
        if (displayElement) {
            if (isValid) {
                displayElement.innerHTML = `<p style="color: #27ae60;">✓ ${dataName}数据验证通过</p>`;
                displayElement.style.display = 'block';
            } else {
                displayElement.innerHTML = `<p style="color: #e74c3c;">✗ ${errorMessage}</p>`;
                displayElement.style.display = 'block';
            }
        } else {
            console.warn(`Display element not found: ${displayId}`);
        }

        // 控制下一步按钮 - 使用更安全的方式查找按钮
        let nextButton = document.getElementById(nextButtonId);
        if (!nextButton) {
            // 尝试通过其他方式查找按钮
            nextButton = document.querySelector(`[id="${nextButtonId}"]`);
        }

        if (nextButton) {
            nextButton.disabled = !isValid;
            nextButton.style.opacity = isValid ? '1' : '0.5';
        } else {
            console.warn(`Next button not found: ${nextButtonId}`);
        }

        return isValid ? data : null;
    },

    // 提交实验数据
    submitExperiment: function(studentId, studentName, experimentData, experimentType) {
        const submitData = {
            student_id: studentId,
            student_name: studentName,
            experiment_type: experimentType,
            ...experimentData
        };

        return fetch('/submit_experiment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(submitData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        });
    },

    // 切换计算器显示
    toggleCalculator: function(calculatorId) {
        const calculator = document.getElementById(calculatorId);
        if (calculator) {
            calculator.style.display = calculator.style.display === 'none' ? 'block' : 'none';
        }
    },

    // 关闭实验提交模态框
    closeExperimentSubmitModal: function(modalId, statusId) {
        const modal = document.getElementById(modalId);
        const status = document.getElementById(statusId);

        if (modal) {
            modal.style.display = 'none';
        }
        if (status) {
            status.style.display = 'none';
        }
    },

    // 通用步骤导航函数
    nextStep: function(currentStep) {
        const current = document.getElementById(`step${currentStep}`);
        const next = document.getElementById(`step${currentStep + 1}`);

        if (current && next) {
            current.classList.remove('active');
            next.classList.add('active');
            next.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    },

    prevStep: function(currentStep) {
        const current = document.getElementById(`step${currentStep}`);
        const prev = document.getElementById(`step${currentStep - 1}`);

        if (current && prev) {
            current.classList.remove('active');
            prev.classList.add('active');
            prev.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    },

    // 通用子步骤导航函数
    nextSubStep: function(stepId, currentSubstep, totalSubsteps) {
        const current = document.getElementById(`${stepId}-substep${currentSubstep}`);
        const next = document.getElementById(`${stepId}-substep${currentSubstep + 1}`);

        if (current && next) {
            current.classList.remove('active');
            next.classList.add('active');

            // 更新进度条
            const progress = document.getElementById(`${stepId}-progress`);
            if (progress) {
                progress.style.width = `${(currentSubstep / totalSubsteps) * 100}%`;
            }

            next.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    },

    prevSubStep: function(stepId, currentSubstep, totalSubsteps) {
        const current = document.getElementById(`${stepId}-substep${currentSubstep}`);
        const prev = document.getElementById(`${stepId}-substep${currentSubstep - 1}`);

        if (current && prev) {
            current.classList.remove('active');
            prev.classList.add('active');

            // 更新进度条
            const progress = document.getElementById(`${stepId}-progress`);
            if (progress) {
                progress.style.width = `${((currentSubstep - 2) / totalSubsteps) * 100}%`;
            }

            prev.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    },

    // 通用数据汇总表生成函数
    generateExperimentSummaryTable: function(tableBodyId, dataConfig) {
        const tbody = document.getElementById(tableBodyId);
        if (!tbody) return;

        tbody.innerHTML = '';

        dataConfig.forEach(item => {
            const row = document.createElement('tr');
            const nameCell = document.createElement('td');
            const valueCell = document.createElement('td');

            nameCell.textContent = item.name;
            valueCell.textContent = item.value + (item.unit || '');

            row.appendChild(nameCell);
            row.appendChild(valueCell);
            tbody.appendChild(row);
        });
    },

    // 通用全屏表格函数
    openFullscreenTable: function(tableId) {
        const table = document.getElementById(tableId);
        if (table) {
            table.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }
    },

    closeFullscreenTable: function(tableId) {
        const table = document.getElementById(tableId);
        if (table) {
            table.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    },

    // 通用图形生成函数
    generatePlot: function(endpoint, data, plotImageId, plotContainerId) {
        fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.error) {
                throw new Error(result.error);
            }
            const plotImage = document.getElementById(plotImageId);
            const plotContainer = document.getElementById(plotContainerId);

            if (plotImage) {
                plotImage.src = 'data:image/png;base64,' + result.plot_url;
                plotImage.style.display = 'block';
            }

            if (plotContainer) {
                plotContainer.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('生成图形时发生错误：' + error.message);
        });
    },

    // 通用数据收集函数
    collectInputData: function(className) {
        const inputs = document.getElementsByClassName(className);
        const data = [];

        for (let input of inputs) {
            const value = parseFloat(input.value);
            if (!isNaN(value)) {
                data.push(value);
            }
        }

        return data;
    },

    // 通用数据验证函数（检查是否所有数据都已填写）
    validateAllDataFilled: function(classNames, dataNames) {
        for (let i = 0; i < classNames.length; i++) {
            const inputs = document.getElementsByClassName(classNames[i]);
            for (let input of inputs) {
                if (!input.value || isNaN(parseFloat(input.value))) {
                    alert(`请填写所有${dataNames[i]}数据！`);
                    return false;
                }
            }
        }
        return true;
    },

    // 通用计算平均值函数
    calculateAverage: function(values) {
        if (values.length === 0) return 0;
        return values.reduce((sum, val) => sum + val, 0) / values.length;
    },

    // 通用计算标准差函数
    calculateStandardDeviation: function(values) {
        if (values.length <= 1) return 0;
        const mean = this.calculateAverage(values);
        const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (values.length - 1);
        return Math.sqrt(variance);
    }
};

// 导出组件和工具函数供其他文件使用
if (typeof window !== 'undefined') {
    window.ExperimentNavigationComponent = ExperimentNavigationComponent;
    window.ProgressBarComponent = ProgressBarComponent;
    window.DataInputTableComponent = DataInputTableComponent;
    window.CalculationInputComponent = CalculationInputComponent;
    window.ExperimentSubmissionModalComponent = ExperimentSubmissionModalComponent;

    // 导出工具函数到全局作用域
    window.analyzeWithAI = ExperimentUtils.analyzeWithAI;
    window.displayAnalysisResult = ExperimentUtils.displayAnalysisResult;
    window.validateMeasurementData = ExperimentUtils.validateMeasurementData;
    window.submitExperiment = ExperimentUtils.submitExperiment;
    window.toggleCalculator = ExperimentUtils.toggleCalculator;
    window.closeExperimentSubmitModal = ExperimentUtils.closeExperimentSubmitModal;
    window.nextStep = ExperimentUtils.nextStep;
    window.prevStep = ExperimentUtils.prevStep;
    window.nextSubStep = ExperimentUtils.nextSubStep;
    window.prevSubStep = ExperimentUtils.prevSubStep;
    window.generateExperimentSummaryTable = ExperimentUtils.generateExperimentSummaryTable;
    window.openFullscreenTable = ExperimentUtils.openFullscreenTable;
    window.closeFullscreenTable = ExperimentUtils.closeFullscreenTable;
    window.generatePlot = ExperimentUtils.generatePlot;
    window.collectInputData = ExperimentUtils.collectInputData;
    window.validateAllDataFilled = ExperimentUtils.validateAllDataFilled;
    window.calculateAverage = ExperimentUtils.calculateAverage;
    window.calculateStandardDeviation = ExperimentUtils.calculateStandardDeviation;
    window.ExperimentUtils = ExperimentUtils;
}
