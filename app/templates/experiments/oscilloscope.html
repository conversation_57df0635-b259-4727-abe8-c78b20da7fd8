<!-- ./app/templates/experiments/oscilloscope.html -->
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>示波器和函数发生器实验指南</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/experiment.css') }}">
    <!-- 引入必要的库 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
    <script src="https://cdn.jsdelivr.net/npm/vue@3.4.27/dist/vue.global.prod.js"></script>
</head>

<body>
    <div id="app" class="container">
        <h1>示波器和函数发生器实验指南</h1>

        <!-- 步骤1: 实验目的 -->
        <div class="step-container" :class="{ 'active': currentStep === 1 }" id="step1">
            <div class="step-header">
                <div class="step-number">1</div>
                <div class="step-title">实验目的</div>
            </div>

            <ol>
                <li>了解模拟示波器的原理和结构。</li>
                <li>掌握函数发生器的基本操作，能够产生指定波形的信号。</li>
                <li>掌握示波器的基本操作，学会使用示波器测量信号的频率和周期。</li>
                <li>了解利萨茹图形的原理，并能使用示波器观察和分析。</li>
                <li>培养动手实践能力和科学探究精神。
            </ol>

            <experiment-navigation-component
                :current-step="1"
                :show-prev="false"
                :is-final-substep="true"
                @next="handleNextStep">
            </experiment-navigation-component>
        </div>

        <!-- 步骤2: 示波器的结构和原理 -->
        <div class="step-container" :class="{ 'active': currentStep === 2 }" id="step2">
            <div class="step-header">
                <div class="step-number">2</div>
                <div class="step-title">示波器的结构和原理</div>
            </div>

            <progress-bar-component
                step-id="step2"
                :current-substep="steps.step2.currentSubstep"
                :total-substeps="steps.step2.totalSubsteps">
            </progress-bar-component>


            <!-- 子步骤2.1: 示波器原理 -->
            <div class="substep-container" :class="{ 'active': steps.step2.currentSubstep === 1 }" id="step2-substep1">
                <div class="substep-header">
                    <div class="substep-number">1</div>
                    <div class="substep-title">示波器原理</div>
                </div>
                <div class="substep-content">
                    <p>示波器是可视化电信号随时间变化的重要工具。其工作原理可以分解为以下几个部分：</p>

                    <h4>工作原理</h4>
                    <ol>
                        <li><strong>电子束扫描</strong>：示波管（或数字示波器的等效原理）中的电子束在荧光屏上扫描形成亮点</li>
                        <li><strong>垂直偏转（Y轴-电压）</strong>：输入信号经过放大后控制电子束的垂直偏转，电压越大，偏转越大</li>
                        <li><strong>水平扫描（X轴-时间）</strong>：内部扫描电路控制电子束的水平匀速扫描，时间越长，水平移动距离越大</li>
                        <li><strong>触发同步</strong>：使扫描与输入信号同步，显示稳定的波形</li>
                    </ol>

                    <div class="instruction">
                        <strong>提示：</strong> 示波器就像是电信号的"照相机"，能够将看不见的电信号变成可见的波形图像。
                    </div>

                    <experiment-navigation-component
                        :current-step="2"
                        :current-substep="1"
                        :total-substeps="steps.step2.totalSubsteps"
                        :show-prev="true"
                        prev-text="上一步"
                        @prev="handlePrevStep"
                        @next="handleNextSubstep">
                    </experiment-navigation-component>
                </div>
            </div>

            <!-- 子步骤2.2: 触发原理 -->
            <div class="substep-container" :class="{ 'active': steps.step2.currentSubstep === 2 }" id="step2-substep2">
                <div class="substep-header">
                    <div class="substep-number">2</div>
                    <div class="substep-title">触发原理</div>
                </div>
                <div class="substep-content">
                    <p>触发是示波器稳定显示波形的关键：</p>
                    <ul>
                        <li><strong>触发的作用</strong>：使扫描与输入信号同步，显示稳定的波形</li>
                        <li><strong>触发电平</strong>：当信号电压达到触发电平时，开始一次扫描</li>
                        <li><strong>触发沿</strong>：可选择上升沿触发（信号从低到高通过触发电平时触发）或下降沿触发（信号从高到低通过触发电平时触发）</li>
                        <li><strong>触发源</strong>：内部触发（通常是CH1）、外部触发、线路触发等</li>
                        <li><strong>触发方式</strong>：
                            <ul>
                                <li>自动触发(Auto)：即使没有满足触发条件也会周期性扫描</li>
                                <li>常态触发(Normal)：只有满足触发条件时才扫描</li>
                                <li>单次触发(Single)：触发一次后停止扫描</li>
                            </ul>
                        </li>
                    </ul>

                    <experiment-navigation-component
                        :current-step="2"
                        :current-substep="2"
                        :total-substeps="steps.step2.totalSubsteps"
                        @prev="handlePrevSubstep"
                        @next="handleNextSubstep">
                    </experiment-navigation-component>
                </div>
            </div>

            <!-- 子步骤2.3: 函数发生器原理 -->
            <div class="substep-container" :class="{ 'active': steps.step2.currentSubstep === 3 }" id="step2-substep3">
                <div class="substep-header">
                    <div class="substep-number">3</div>
                    <div class="substep-title">函数发生器原理</div>
                </div>
                <div class="substep-content">
                    <p>函数发生器是产生各种波形信号的电子设备，作为实验的信号源。</p>

                    <h4>基本工作原理</h4>
                    <p>函数发生器通过电子电路或数字合成技术产生不同波形的周期性变化电压信号。</p>

                    <h4>主要参数</h4>
                    <ul>
                        <li><strong>波形类型</strong>：
                            <ul>
                                <li>正弦波(Sine)：最基本的周期信号，无谐波</li>
                                <li>方波(Square)：高低电平交替的信号</li>
                                <li>三角波(Triangle)：线性上升和下降的信号</li>
                                <li>锯齿波(Sawtooth)：线性上升或下降后快速返回的信号</li>
                            </ul>
                        </li>
                        <li><strong>频率(Frequency)</strong>：信号变化的快慢，单位Hz, kHz, MHz等</li>
                        <li><strong>幅度(Amplitude)</strong>：信号电压的大小，常用单位是峰峰值Vpp (Volts peak-to-peak)</li>
                        <li><strong>直流偏置(DC Offset)</strong>：在交流信号上叠加一个直流电压</li>
                        <li><strong>占空比(Duty Cycle)</strong>：针对方波，高电平在一个周期内所占的时间比例</li>
                    </ul>

                    <div class="success-message" style="display: block;">
                        实验原理学习完成！您可以继续下一步实验。
                    </div>

                    <experiment-navigation-component
                        :current-step="2"
                        :current-substep="3"
                        :total-substeps="steps.step2.totalSubsteps"
                        :is-final-substep="true"
                        next-text="进入下一步"
                        @prev="handlePrevSubstep"
                        @next="handleNextStep">
                    </experiment-navigation-component>
                </div>
            </div>
        </div>

        <!-- 步骤3: 实验设备 -->
        <div class="step-container" :class="{ 'active': currentStep === 3 }" id="step3">
            <div class="step-header">
                <div class="step-number">3</div>
                <div class="step-title">实验设备</div>
            </div>

            <ul>
                <li><strong>示波器</strong>：数字示波器(如Tektronix TBS1052B, Rigol DS1054Z)或模拟示波器</li>
                <li><strong>函数发生器</strong>：能够输出正弦波、方波、三角波等常用波形(如Siglent SDG1032X)</li>
                <li><strong>BNC连接线</strong>：至少2根(频率测量实验1根，利萨茹图形实验2根)</li>
                <li><strong>探头补偿调节螺丝刀</strong></li>
            </ul>

            <div class="warning">
                <h4>安全操作</h4>
                <ul>
                    <li>电源插座接地良好，不要用湿手接触电源插头</li>
                    <li>不要带电插拔探头或BNC电缆</li>
                    <li>旋钮调节力度适中，不要用力过猛</li>
                </ul>
            </div>

            <experiment-navigation-component
                :current-step="3"
                :is-step-start="true"
                :is-final-substep="true"
                @prev="handlePrevStep"
                @next="handleNextStep">
            </experiment-navigation-component>
        </div>

        <!-- 步骤4: 函数发生器的使用 -->
        <div class="step-container" :class="{ 'active': currentStep === 4 }" id="step4">
            <div class="step-header">
                <div class="step-number">4</div>
                <div class="step-title">函数发生器的使用</div>
            </div>

            <progress-bar-component
                step-id="step4"
                :current-substep="steps.step4.currentSubstep"
                :total-substeps="steps.step4.totalSubsteps">
            </progress-bar-component>

            <div class="substep-container" :class="{ 'active': steps.step4.currentSubstep === 1 }" id="step4-substep1">
                <div class="substep-header">
                    <div class="substep-number">1</div>
                    <div class="substep-title">认识函数发生器面板</div>
                </div>
                <div class="substep-content">
                    <p>函数发生器面板通常包含以下区域：</p>
                    <ul>
                        <li>电源开关</li>
                        <li>波形选择区(正弦波、方波、三角波等按钮)</li>
                        <li>频率调节区(频率旋钮、频率范围选择)</li>
                        <li>幅度调节区(幅度旋钮、单位选择)</li>
                        <li>直流偏置调节</li>
                        <li>输出接口(BNC)</li>
                    </ul>

                    <experiment-navigation-component
                        :current-step="4"
                        :current-substep="1"
                        :total-substeps="steps.step4.totalSubsteps"
                        :show-prev="true"
                        prev-text="上一步"
                        @prev="handlePrevStep"
                        @next="handleNextSubstep">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" :class="{ 'active': steps.step4.currentSubstep === 2 }" id="step4-substep2">
                <div class="substep-header">
                    <div class="substep-number">2</div>
                    <div class="substep-title">连接电源</div>
                </div>
                <div class="substep-content">
                    <p>连接函数发生器电源线，并打开电源开关。</p>

                    <div class="warning">
                        <strong>注意：</strong> 确保电源插座接地良好，不要用湿手接触电源插头。
                    </div>

                    <experiment-navigation-component
                        :current-step="4"
                        :current-substep="2"
                        :total-substeps="steps.step4.totalSubsteps"
                        @prev="handlePrevSubstep"
                        @next="handleNextSubstep">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" :class="{ 'active': steps.step4.currentSubstep === 3 }" id="step4-substep3">
                <div class="substep-header">
                    <div class="substep-number">3</div>
                    <div class="substep-title">选择波形</div>
                </div>
                <div class="substep-content">
                    <p>按下或旋转波形选择按钮/旋钮，选择需要的波形(例如：正弦波)。</p>

                    <div class="instruction">
                        <strong>提示：</strong> 不同型号的函数发生器选择波形的方式可能不同，有的是按钮，有的是旋钮，请根据实际设备操作。
                    </div>

                    <experiment-navigation-component
                        :current-step="4"
                        :current-substep="3"
                        :total-substeps="steps.step4.totalSubsteps"
                        @prev="handlePrevSubstep"
                        @next="handleNextSubstep">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" :class="{ 'active': steps.step4.currentSubstep === 4 }" id="step4-substep4">
                <div class="substep-header">
                    <div class="substep-number">4</div>
                    <div class="substep-title">设置频率</div>
                </div>
                <div class="substep-content">
                    <p><strong>粗调</strong>：选择合适的频率范围(Hz, kHz, MHz)</p>
                    <p><strong>微调</strong>：旋转频率调节旋钮，设置需要的频率值</p>
                    <p>频率单位和读数方法示例：显示1.000 kHz表示1000Hz</p>

                    <div class="highlight">
                        对于本次实验，请设置频率为1kHz。
                    </div>

                    <experiment-navigation-component
                        :current-step="4"
                        :current-substep="4"
                        :total-substeps="steps.step4.totalSubsteps"
                        @prev="handlePrevSubstep"
                        @next="handleNextSubstep">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" :class="{ 'active': steps.step4.currentSubstep === 5 }" id="step4-substep5">
                <div class="substep-header">
                    <div class="substep-number">5</div>
                    <div class="substep-title">设置幅度</div>
                </div>
                <div class="substep-content">
                    <p>调节幅度旋钮，设置需要的信号幅度(Vpp峰峰值)。</p>

                    <div class="instruction">
                        <strong>提示：</strong> 幅度过大或过小都会影响示波器显示效果。对于初次使用，建议设置为2-5Vpp。
                    </div>

                    <div class="highlight">
                        对于本次实验，请设置幅度为3Vpp。
                    </div>

                    <experiment-navigation-component
                        :current-step="4"
                        :current-substep="5"
                        :total-substeps="steps.step4.totalSubsteps"
                        @prev="handlePrevSubstep"
                        @next="handleNextSubstep">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" :class="{ 'active': steps.step4.currentSubstep === 6 }" id="step4-substep6">
                <div class="substep-header">
                    <div class="substep-number">6</div>
                    <div class="substep-title">设置直流偏置(可选)</div>
                </div>
                <div class="substep-content">
                    <p>如果需要，调节直流偏置旋钮，添加直流偏置电压。</p>

                    <div class="instruction">
                        <strong>提示：</strong> 直流偏置的作用是使波形整体上下移动。对于初次使用，建议将直流偏置设为0V。
                    </div>

                    <div class="highlight">
                        对于本次实验，请设置直流偏置为0V。
                    </div>

                    <experiment-navigation-component
                        :current-step="4"
                        :current-substep="6"
                        :total-substeps="steps.step4.totalSubsteps"
                        @prev="handlePrevSubstep"
                        @next="handleNextSubstep">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" :class="{ 'active': steps.step4.currentSubstep === 7 }" id="step4-substep7">
                <div class="substep-header">
                    <div class="substep-number">7</div>
                    <div class="substep-title">连接输出</div>
                </div>
                <div class="substep-content">
                    <p>使用BNC电缆，将函数发生器的信号输出端口连接到示波器的CH1输入端口。</p>

                    <div class="warning">
                        <strong>注意：</strong> 连接BNC电缆时，应先确保函数发生器的输出关闭或示波器处于关机状态，避免带电插拔。
                    </div>

                    <div class="success-message" style="display: block;">
                        函数发生器设置完成！您可以继续下一步实验。
                    </div>

                    <experiment-navigation-component
                        :current-step="4"
                        :current-substep="7"
                        :total-substeps="steps.step4.totalSubsteps"
                        :is-final-substep="true"
                        next-text="进入下一步"
                        @prev="handlePrevSubstep"
                        @next="handleNextStep">
                    </experiment-navigation-component>
                </div>
            </div>
        </div>

        <!-- 步骤5: 示波器的基本操作 -->
        <div class="step-container" :class="{ 'active': currentStep === 5 }" id="step5">
            <div class="step-header">
                <div class="step-number">5</div>
                <div class="step-title">示波器的基本操作</div>
            </div>

            <progress-bar-component
                step-id="step5"
                :current-substep="steps.step5.currentSubstep"
                :total-substeps="steps.step5.totalSubsteps">
            </progress-bar-component>

            <div class="substep-container" :class="{ 'active': steps.step5.currentSubstep === 1 }" id="step5-substep1">
                <div class="substep-header">
                    <div class="substep-number">1</div>
                    <div class="substep-title">实验前准备</div>
                </div>
                <div class="substep-content">
                    <p>在开始实验前，需要做好以下准备工作：</p>

                    <div class="instruction">
                        <strong>准备步骤：</strong>
                        <ol>
                            <li>检查探头BNC接头是否旋紧连接到示波器CH1(或其他通道)</li>
                            <li>将示波器面板上所有的旋钮调节到竖直朝上状态</li>
                            <li>打开示波器和函数发生器的电源</li>
                        </ol>
                    </div>

                    <div class="warning">
                        <strong>注意：</strong> 确保所有连接都牢固可靠，避免接触不良导致信号不稳定。
                    </div>

                    <experiment-navigation-component
                        :current-step="5"
                        :current-substep="1"
                        :total-substeps="steps.step5.totalSubsteps"
                        :show-prev="true"
                        prev-text="上一步"
                        @prev="handlePrevStep"
                        @next="handleNextSubstep">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" :class="{ 'active': steps.step5.currentSubstep === 2 }" id="step5-substep2">
                <div class="substep-header">
                    <div class="substep-number">2</div>
                    <div class="substep-title">调出波形</div>
                </div>
                <div class="substep-content">
                    <p>调节示波器基本参数，使波形显示在屏幕上：</p>

                    <div class="instruction">
                        <strong>操作步骤：</strong>
                        <ol>
                            <li>根据输入信号，选择通道，将示波器面板上横排9个按钮中的第三个、第四个按钮按下</li>
                            <li>调节滤波开关，选择AC模式 </li>
                            <li>选择触发模式，将示波器面板上横排9个按钮中的第七个按钮按下</li>
                            <li>调节CH1的垂直刻度旋钮(VOLTS/DIV)，从较大值开始逐渐减小</li>
                            <li>调节水平刻度旋钮(SEC/DIV或TIME/DIV)，从较大值开始逐渐减小</li>
                        </ol>
                    </div>

                    <div class="highlight">
                        此阶段的目标是让波形显示在屏幕上，不需要考虑波形是否稳定。
                    </div>

                    <experiment-navigation-component
                        :current-step="5"
                        :current-substep="2"
                        :total-substeps="steps.step5.totalSubsteps"
                        @prev="handlePrevSubstep"
                        @next="handleNextSubstep">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" :class="{ 'active': steps.step5.currentSubstep === 3 }" id="step5-substep3">
                <div class="substep-header">
                    <div class="substep-number">3</div>
                    <div class="substep-title">波形调稳定</div>
                </div>
                <div class="substep-content">
                    <p>调节触发参数，使波形稳定显示：</p>

                    <div class="instruction">
                        <strong>操作步骤：</strong>
                        <ol>
                            <li>根据输入信号的通道选择出通道</li>
                            <li>选择上升沿或下降沿触发</li>
                            <li>调节触发电平旋钮(LEVEL)，直到波形稳定显示</li>
                            <li>微调垂直和水平刻度，使波形大小和周期数适中</li>
                            <li>调整直到屏幕上显示2-3个完整周期，且波形高度占屏幕的60%-80%</li>
                        </ol>
                    </div>

                    <div class="warning">
                        <strong>注意：</strong> 如果触发电平设置不当，波形可能会不停地滚动或显示不稳定。对于正弦波，通常设置在0V附近效果最佳。
                    </div>

                    <experiment-navigation-component
                        :current-step="5"
                        :current-substep="3"
                        :total-substeps="steps.step5.totalSubsteps"
                        @prev="handlePrevSubstep"
                        @next="handleNextSubstep">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" :class="{ 'active': steps.step5.currentSubstep === 4 }" id="step5-substep4">
                <div class="substep-header">
                    <div class="substep-number">4</div>
                    <div class="substep-title">校准示波器</div>
                </div>
                <div class="substep-content">
                    <p>在波形稳定后，进行探头补偿和精确校准：</p>

                    <div class="instruction">
                        <strong>探头补偿步骤：</strong>
                        <ol>
                            <li>将探头连接到CH1</li>
                            <li>找到示波器面板上的校准信号输出端口(Cal 0.3)</li>
                            <li>将探头探针尖端接触校准信号输出端，接地夹连接到校准信号的接地端</li>
                            <li>观察示波器屏幕上的波形(应为方波)</li>
                            <li>判断补偿状态：
                                <ul>
                                    <li>过补偿：波形顶部有尖峰</li>
                                    <li>欠补偿：波形顶部圆钝</li>
                                    <li>良好补偿：波形平坦，上升沿和下降沿陡峭</li>
                                </ul>
                            </li>
                            <li>使用探头附带的塑料调节螺丝刀，轻轻旋转探头上的补偿电容调节螺丝，直到波形达到良好补偿的状态</li>
                        </ol>
                    </div>

                    <div class="highlight">
                        只有在波形稳定显示后，才能进行精确的校准和测量。校准完成后，可以开始测量周期和电压峰峰值。
                    </div>

                    <div class="success-message" style="display: block;">
                        示波器基本操作设置完成！您可以继续下一步实验。
                    </div>

                    <experiment-navigation-component
                        :current-step="5"
                        :current-substep="4"
                        :total-substeps="steps.step5.totalSubsteps"
                        :is-final-substep="true"
                        next-text="进入下一步"
                        @prev="handlePrevSubstep"
                        @next="handleNextStep">
                    </experiment-navigation-component>
                </div>
            </div>
        </div>

        <!-- 步骤6: 频率和电压峰峰值测量实验 -->
        <div class="step-container" :class="{ 'active': currentStep === 6 }" id="step6">
            <div class="step-header">
                <div class="step-number">6</div>
                <div class="step-title">频率和电压峰峰值测量</div>
            </div>

            <progress-bar-component
                step-id="step6"
                :current-substep="steps.step6.currentSubstep"
                :total-substeps="steps.step6.totalSubsteps">
            </progress-bar-component>

            <div class="substep-container" :class="{ 'active': steps.step6.currentSubstep === 1 }" id="step6-substep1">
                <div class="substep-header">
                    <div class="substep-number">1</div>
                    <div class="substep-title">设置函数发生器</div>
                </div>
                <div class="substep-content">
                    <p>设置函数发生器输出一个已知频率的正弦波信号：</p>

                    <div class="instruction">
                        <strong>设置步骤：</strong>
                        <ol>
                            <li>设置函数发生器输出为正弦波</li>
                            <li>设置频率约200~1kHz</li>
                            <li>设置幅度约1~3Vpp</li>
                        </ol>
                    </div>

                    <experiment-navigation-component
                        :current-step="6"
                        :current-substep="1"
                        :total-substeps="steps.step6.totalSubsteps"
                        :show-prev="true"
                        prev-text="上一步"
                        @prev="handlePrevStep"
                        @next="handleNextSubstep">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" :class="{ 'active': steps.step6.currentSubstep === 2 }" id="step6-substep2">
                <div class="substep-header">
                    <div class="substep-number">2</div>
                    <div class="substep-title">测量信号参数</div>
                </div>
                <div class="substep-content">
                    <p>使用示波器测量信号的周期和电压峰峰值：</p>

                    <div class="instruction">
                        <strong>测量方法：</strong>
                        <ol>
                            <li>确保波形已稳定显示在屏幕上</li>
                            <li>观察一个完整周期占用的水平格数</li>
                            <li>周期T = 水平格数 × 时间/格</li>
                            <li>频率f = 1/T</li>
                            <li>观察波形峰峰值占用的垂直格数</li>
                            <li>峰峰值电压 = 垂直格数 × 电压/格</li>
                        </ol>
                    </div>

                    <experiment-navigation-component
                        :current-step="6"
                        :current-substep="2"
                        :total-substeps="steps.step6.totalSubsteps"
                        @prev="handlePrevSubstep"
                        @next="handleNextSubstep">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" :class="{ 'active': steps.step6.currentSubstep === 3 }" id="step6-substep3">
                <div class="substep-header">
                    <div class="substep-number">3</div>
                    <div class="substep-title">记录和分析数据</div>
                </div>
                <div class="substep-content">
                    <p>记录测量数据并与函数发生器设置值进行比较：</p>

                    <button class="table-btn" onclick="openFullscreenTable('fullscreenTable')">打开全屏数据表格</button>

                    <div class="instruction">
                        <strong>分析要点：</strong>
                        <ul>
                            <li>比较测量的频率与函数发生器设置的频率</li>
                            <li>比较测量的电压与函数发生器设置的电压</li>
                            <li>计算误差百分比</li>
                            <li>分析可能的误差来源</li>
                        </ul>
                    </div>

                    <div class="success-message" style="display: block;">
                        频率测量实验完成！您可以继续下一步实验。
                    </div>

                    <experiment-navigation-component
                        :current-step="6"
                        :current-substep="3"
                        :total-substeps="steps.step6.totalSubsteps"
                        :is-final-substep="true"
                        next-text="进入下一步"
                        :next-button-id="'step6-next-btn'"
                        :is-disabled="!steps.step6.validated"
                        @prev="handlePrevSubstep"
                        @next="handleNextStep">
                    </experiment-navigation-component>
                </div>
            </div>
        </div>

        <!-- 步骤7: 利萨茹图形实验 -->
        <div class="step-container" :class="{ 'active': currentStep === 7 }" id="step7">
            <div class="step-header">
                <div class="step-number">7</div>
                <div class="step-title">利萨茹图形实验</div>
            </div>

            <progress-bar-component
                step-id="step7"
                :current-substep="steps.step7.currentSubstep"
                :total-substeps="steps.step7.totalSubsteps">
            </progress-bar-component>

            <div class="substep-container" :class="{ 'active': steps.step7.currentSubstep === 1 }" id="step7-substep1">
                <div class="substep-header">
                    <div class="substep-number">1</div>
                    <div class="substep-title">利萨茹图形原理</div>
                </div>
                <div class="substep-content">
                    <p>利萨茹图形是两个正弦波在X-Y模式下合成的图形，可用于分析两个信号的频率比和相位差。</p>

                    <div class="instruction">
                        <strong>利萨茹图形的特点：</strong>
                        <ul>
                            <li>当两个信号频率相等时，图形可以是圆形、椭圆形或直线，取决于相位差</li>
                            <li>当两个信号频率比为整数比时，图形会形成复杂的闭合曲线</li>
                            <li>频率比可以通过图形的交点数确定</li>
                        </ul>
                    </div>

                    <experiment-navigation-component
                        :current-step="7"
                        :current-substep="1"
                        :total-substeps="steps.step7.totalSubsteps"
                        :show-prev="true"
                        prev-text="上一步"
                        @prev="handlePrevStep"
                        @next="handleNextSubstep">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" :class="{ 'active': steps.step7.currentSubstep === 2 }" id="step7-substep2">
                <div class="substep-header">
                    <div class="substep-number">2</div>
                    <div class="substep-title">连接设备</div>
                </div>
                <div class="substep-content">
                    <p>连接函数发生器输出到示波器的两个通道。</p>

                    <div class="instruction">
                        <strong>连接步骤：</strong>
                        <ol>
                            <li>将函数发生器的第一个输出连接到示波器的CH1。</li>
                            <li>将函数发生器的第二个输出连接到示波器的CH2。</li>
                            <li>调节两个示波器的频率都是50Hz，电压峰峰值为1Vpp，在示波器上观察到两个通道的标准正弦波。</li>
                        </ol>
                    </div>

                    <div class="warning">
                        <strong>注意：</strong> 确保所有连接都牢固，避免信号不稳定。
                    </div>

                    <experiment-navigation-component
                        :current-step="7"
                        :current-substep="2"
                        :total-substeps="steps.step7.totalSubsteps"
                        @prev="handlePrevSubstep"
                        @next="handleNextSubstep">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" :class="{ 'active': steps.step7.currentSubstep === 3 }" id="step7-substep3">
                <div class="substep-header">
                    <div class="substep-number">3</div>
                    <div class="substep-title">设置X-Y模式</div>
                </div>
                <div class="substep-content">
                    <p>将示波器设置为X-Y显示模式。</p>

                    <div class="instruction">
                        <strong>设置步骤：</strong>
                        <ol>
                            <li>将Volts/div旋钮反时针旋转到x-y这一档</li>
                            <li>观察莉萨如图形</li>
                        </ol>
                    </div>

                    <experiment-navigation-component
                        :current-step="7"
                        :current-substep="3"
                        :total-substeps="steps.step7.totalSubsteps"
                        @prev="handlePrevSubstep"
                        @next="handleNextSubstep">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" :class="{ 'active': steps.step7.currentSubstep === 4 }" id="step7-substep4">
                <div class="substep-header">
                    <div class="substep-number">4</div>
                    <div class="substep-title">观察利萨茹图形</div>
                </div>
                <div class="substep-content">
                    <p>调整函数发生器的频率和相位，观察不同的利萨茹图形。</p>

                    <div class="instruction">
                        <strong>实验步骤：</strong>
                        <ol>
                            <li>按照下表中的频率设置，依次调整CH1和CH2的频率</li>
                            <li>观察每种频率比下形成的利萨茹图形</li>
                            <li>记录观察到的图形形状和特点</li>
                            <li>尝试微调相位，观察图形的变化</li>
                        </ol>
                    </div>

                    <div class="table-container">
                        <table class="lissajous-table">
                            <tr>
                                <th>实验序号</th>
                                <th>CH1频率(Hz)</th>
                                <th>CH2频率(Hz)</th>
                                <th>频率比(CH1:CH2)</th>
                            </tr>
                            <tr>
                                <td>1</td>
                                <td>50</td>
                                <td>50</td>
                                <td>1:1</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>50</td>
                                <td>100</td>
                                <td>1:2</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>50</td>
                                <td>150</td>
                                <td>1:3</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>100</td>
                                <td>150</td>
                                <td>2:3</td>
                            </tr>
                        </table>
                    </div>

                    <div class="highlight">
                        利萨茹图形可以用于精确测量两个信号的频率比和相位差。不同频率比会形成不同的图案，如8字形、环形等复杂图案。
                    </div>

                    <div class="success-message" style="display: block;">
                        利萨茹图形实验完成！您已完成所有实验步骤。
                    </div>

                    <experiment-navigation-component
                        :current-step="7"
                        :current-substep="4"
                        :total-substeps="steps.step7.totalSubsteps"
                        :is-final-substep="true"
                        next-text="完成实验"
                        @prev="handlePrevSubstep"
                        @next="finishExperiment">
                    </experiment-navigation-component>
                </div>
            </div>
        </div>

        <div class="footer">
            © 2024 | 物理实验基础课程平台 | 网页设计：朱瑞华
        </div>
    </div>

    <!-- 全屏数据表格 -->
    <div id="fullscreenTable" class="fullscreen-table" style="display: none;">
        <div class="fullscreen-content">
            <div class="fullscreen-header">
                <h2>频率测量数据记录表</h2>
                <button class="close-fullscreen" onclick="closeFullscreenTable()">×</button>
            </div>
            <div class="measurement-container">
                <div class="measurement-instruction">
                    <p>请在下表中填写您的测量数据。您可以横向旋转设备以获得更好的显示效果。</p>
                </div>



            <div class="table-container" style="overflow-x: auto;">
                <table class="horizontal-table">
                    <thead>
                        <tr>
                            <th rowspan="2">测量项目</th>
                            <th colspan="2">函数信号发生器设置</th>
                            <th colspan="4">示波器测量</th>
                            <th colspan="3">计算结果</th>
                        </tr>
                        <tr>
                            <th>峰峰值电压 (Vpp)</th>
                            <th>频率 (Hz)</th>
                            <th>每格电压值 (V/div)</th>
                            <th>峰峰值电压格数</th>
                            <th>每格时间 (ms/div)</th>
                            <th>周期格数</th>
                            <th>测量电压 (Vpp)</th>
                            <th>测量周期 (ms)</th>
                            <th>测量频率 (Hz)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>正弦波信号</td>
                            <td><input type="number" id="voltage" step="0.01" oninput="calculateHorizontal()" placeholder="3.00"></td>
                            <td><input type="number" id="freq" step="0.1" oninput="calculateHorizontal()" placeholder="1000"></td>
                            <td><input type="number" id="voltPerDiv" step="0.01" oninput="calculateHorizontal()" placeholder="0.5"></td>
                            <td><input type="number" id="voltDiv" step="0.1" oninput="calculateHorizontal()" placeholder="6.0"></td>
                            <td><input type="number" id="timePerDiv" step="0.01" oninput="calculateHorizontal()" placeholder="0.2"></td>
                            <td><input type="number" id="periodDiv" step="0.1" oninput="calculateHorizontal()" placeholder="5.0"></td>
                            <td id="calc_voltage" style="background-color: #f8f9fa;">-</td>
                            <td id="calc_period" style="background-color: #f8f9fa;">-</td>
                            <td id="calc_frequency" style="background-color: #f8f9fa;">-</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="measurement-instruction" style="margin-top: 20px;">
                <strong>填表说明：</strong>
                <ul>
                    <li><strong>函数信号发生器</strong>部分填写您在函数发生器上设置的值</li>
                    <li><strong>每格电压值</strong>填写示波器上设置的垂直刻度值(V/div)</li>
                    <li><strong>峰峰值电压格数</strong>填写波形在垂直方向占用的格数</li>
                    <li><strong>每格时间</strong>填写示波器上设置的水平刻度值(ms/div或μs/div)</li>
                    <li><strong>周期格数</strong>填写一个完整波形周期占用的水平格数</li>
                    <li><strong>计算结果</strong>列会自动计算显示</li>
                </ul>
            </div>

            <div id="analysisLoading" class="loading-indicator" style="display: none;">
                <div class="loading-spinner"></div>
                <p>正在分析您的测量数据...</p>
            </div>
            <div id="analysisResult" class="analysis-container" style="display: none;">
                <h3>AI分析结果</h3>
                <div id="analysisContent" class="analysis-content"></div>
            </div>

            <div class="measurement-buttons">
                <button class="btn-submit" onclick="submitMeasurementHorizontal()">提交测量数据分析</button>
                <button class="btn-close" onclick="closeFullscreenTable('fullscreenTable')">关闭表格</button>
            </div>
        </div>
    </div>

    <!-- 学生信息提交模态框 -->
    <experiment-submission-modal
        experiment-type="oscilloscope"
        modal-id="oscilloscope-submission-modal"
        :visible="showSubmissionModal"
        @submit="handleSubmitExperiment"
        @cancel="handleCancelSubmission"
        @success="handleSubmissionSuccess"
        ref="submissionModal">
    </experiment-submission-modal>

    <!-- 引入必要的库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/showdown/2.1.0/showdown.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/showdown-katex@0.8.0/dist/showdown-katex.min.js"></script>

</script>
    <script src="{{ url_for('static', filename='js/vue-components.js') }}"></script>

    <!-- 示波器实验特有脚本 -->
    <script>
        // 横向表格的计算功能
        function calculateHorizontal() {
            const voltPerDiv = Number(document.getElementById('voltPerDiv').value);
            const voltDiv = Number(document.getElementById('voltDiv').value);
            const timePerDiv = Number(document.getElementById('timePerDiv').value);
            const periodDiv = Number(document.getElementById('periodDiv').value);

            // 计算峰峰值电压
            const peakVoltage = voltPerDiv * voltDiv;
            document.getElementById('calc_voltage').textContent = isNaN(peakVoltage) ? '-' : peakVoltage.toFixed(2);

            // 计算周期 (ms)
            const periodMs = timePerDiv * periodDiv;
            document.getElementById('calc_period').textContent = isNaN(periodMs) ? '-' : periodMs.toFixed(3);

            // 计算频率 (Hz)
            const frequencyHz = periodMs > 0 ? (1 / (periodMs / 1000)) : 0;
            document.getElementById('calc_frequency').textContent = isNaN(frequencyHz) || periodMs <= 0 ? '-' : frequencyHz.toFixed(3);
        }

        // 横向表格的提交功能
        function submitMeasurementHorizontal() {
            const generatorVoltage = Number(document.getElementById('voltage').value);
            const generatorFreq = Number(document.getElementById('freq').value);
            const voltPerDiv = Number(document.getElementById('voltPerDiv').value);
            const voltDiv = Number(document.getElementById('voltDiv').value);
            const measuredVoltage = voltPerDiv * voltDiv;
            const timePerDiv = Number(document.getElementById('timePerDiv').value);
            const periodDiv = Number(document.getElementById('periodDiv').value);
            const periodMs = timePerDiv * periodDiv;
            const measuredFreq = periodMs > 0 ? (1 / (periodMs / 1000)) : 0;

            if (isNaN(generatorVoltage) || isNaN(generatorFreq) || isNaN(voltPerDiv) ||
                isNaN(voltDiv) || isNaN(timePerDiv) || isNaN(periodDiv)) {
                alert('请填写所有必要的测量数据！');
                return;
            }

            document.getElementById('analysisLoading').style.display = 'block';
            document.getElementById('analysisResult').style.display = 'none';
            const selectedModelId = 'default';

            const measurementData = {
                generator: {
                    voltage: generatorVoltage,
                    frequency: generatorFreq
                },
                oscilloscope: {
                    voltPerDiv: voltPerDiv,
                    voltDiv: voltDiv,
                    measuredVoltage: measuredVoltage,
                    timePerDiv: timePerDiv,
                    periodDiv: periodDiv,
                    period: periodMs,
                    measuredFrequency: measuredFreq
                }
            };

            localStorage.setItem('oscilloscopeData', JSON.stringify(measurementData));

            analyzeWithAI('/oscilloscope/analyze-measurement', measurementData, selectedModelId,
                (renderedHTML) => {
                    document.getElementById('analysisLoading').style.display = 'none';
                    displayAnalysisResult('analysisResult', 'analysisContent', renderedHTML);
                    // 启用"进入下一步"按钮
                    app.steps.step6.validated = true;
                },
                (error) => {
                    document.getElementById('analysisLoading').style.display = 'none';
                    displayAnalysisResult('analysisResult', 'analysisContent',
                        `<p style="color: #e74c3c;">分析失败: ${error}</p>`);
                }
            );
        }



        // 提交实验数据
        function submitExperimentData() {
            const studentId = document.getElementById('studentId').value.trim();
            const studentName = document.getElementById('studentName').value.trim();

            // 验证学生信息
            if (!studentId || !studentName) {
                alert('请输入学号和姓名！');
                return;
            }

            // 获取之前保存的实验数据
            const oscilloscopeData = JSON.parse(localStorage.getItem('oscilloscopeData') || '{}');

            // 获取分析结果
            const analysisContent = document.getElementById('analysisContent');
            let analysisResultText = null;
            if (analysisContent && analysisContent.innerHTML &&
                document.getElementById('analysisResult').style.display !== 'none') {
                analysisResultText = analysisContent.innerText || analysisContent.textContent;
            }

            // 验证实验数据是否完整
            if (!oscilloscopeData.generator || !oscilloscopeData.oscilloscope) {
                alert('请先完成测量数据的填写和分析！');
                return;
            }

            // 准备提交数据
            const submissionData = {
                experiment_type: 'oscilloscope',
                student_id: studentId,
                student_name: studentName,
                measurement_data: oscilloscopeData,
                analysis_result: analysisResultText,
                submit_time: new Date().toISOString()
            };

            // 使用通用提交处理函数
            handleExperimentSubmission('oscilloscope', submissionData,
                // 成功回调
                (result) => {
                    closeStudentInfoModal();
                    alert(`实验数据提交成功！\n记录ID: ${result.record_id}\n版本: ${result.version}`);
                    localStorage.removeItem('oscilloscopeData');
                },
                // 错误回调
                (error) => {
                    alert(`提交失败: ${error}`);
                }
            );
        }

        // 引入Vue
        const { createApp, ref, reactive } = Vue;

        const app = createApp({
            components: {
                'experiment-navigation-component': ExperimentNavigationComponent,
                'progress-bar-component': ProgressBarComponent,
                'experiment-submission-modal': ExperimentSubmissionModalComponent
            },
            setup() {
                const currentStep = ref(1);
                const showSubmissionModal = ref(false);
                const steps = reactive({
                    step2: { currentSubstep: 1, totalSubsteps: 3 },
                    step4: { currentSubstep: 1, totalSubsteps: 7 },
                    step5: { currentSubstep: 1, totalSubsteps: 4 },
                    step6: { currentSubstep: 1, totalSubsteps: 3, validated: false },
                    step7: { currentSubstep: 1, totalSubsteps: 4 },
                });

                const scrollToStep = (stepNumber) => {
                    const stepElement = document.getElementById(`step${stepNumber}`);
                    if (stepElement) {
                        stepElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }
                };

                const handleNextStep = (step) => {
                    if (steps[`step${step + 1}`]) {
                        steps[`step${step + 1}`].currentSubstep = 1;
                    }
                    currentStep.value = step + 1;
                    scrollToStep(currentStep.value);
                };

                const handlePrevStep = (step) => {
                    if (step > 1) {
                        currentStep.value = step - 1;
                        // 如果上一步有子步骤，则跳转到最后一个子步骤
                        if (steps[`step${step - 1}`] && steps[`step${step - 1}`].totalSubsteps > 1) {
                           steps[`step${step - 1}`].currentSubstep = steps[`step${step - 1}`].totalSubsteps;
                        }
                        scrollToStep(currentStep.value);
                    }
                };

                const handleNextSubstep = (stepId, currentSubstep, totalSubsteps) => {
                    const stepKey = `step${stepId.replace('step', '')}`;
                    if (steps[stepKey] && steps[stepKey].currentSubstep < steps[stepKey].totalSubsteps) {
                        steps[stepKey].currentSubstep++;
                    }
                };

                const handlePrevSubstep = (stepId, currentSubstep, totalSubsteps) => {
                    const stepKey = `step${stepId.replace('step', '')}`;
                    if (steps[stepKey] && steps[stepKey].currentSubstep > 1) {
                        steps[stepKey].currentSubstep--;
                    }
                };

                const finishExperiment = () => {
                    // 检查是否已完成测量
                    const oscilloscopeData = localStorage.getItem('oscilloscopeData');
                    if (!oscilloscopeData) {
                        alert('请先完成测量数据的填写和分析！');
                        return;
                    }
                    showSubmissionModal.value = true;
                };

                const submitMeasurementHorizontal = () => {
                    showSubmissionModal.value = true;
                };

                const handleSubmitExperiment = (submissionData) => {
                    // 收集实验数据
                    const experimentData = collectExperimentData();
                    submitExperiment(submissionData.studentId, submissionData.studentName, experimentData);
                };

                const handleCancelSubmission = () => {
                    showSubmissionModal.value = false;
                };

                const handleSubmissionSuccess = () => {
                    showSubmissionModal.value = false;
                    alert('实验已成功完成并提交！');
                };

                const collectExperimentData = () => {
                    return { experimentData: window.experimentData || {} };
                };

                const submitExperiment = async (studentId, studentName, experimentData) => {
                    try {
                        // 这里可以调用实际的提交API
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        console.log('实验数据已提交', { studentId, studentName, experimentData });
                    } catch (error) {
                        console.error('提交失败：', error.message);
                    }
                };

                // 添加全屏表格函数
                const openFullscreenTable = (tableId) => {
                    const table = document.getElementById(tableId);
                    if (table) {
                        table.style.display = 'block';
                        // 滚动到表格顶部
                        table.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }
                };

                // 添加关闭全屏表格函数
                const closeFullscreenTable = (tableId) => {
                    const table = document.getElementById(tableId);
                    if (table) {
                        table.style.display = 'none';
                        // 如果是全屏模式，退出全屏
                        if (document.fullscreenElement) {
                            document.exitFullscreen();
                        }
                    }
                };

                // 将函数暴露到全局作用域
                window.openFullscreenTable = openFullscreenTable;
                window.closeFullscreenTable = closeFullscreenTable;

                return {
                    currentStep,
                    showSubmissionModal,
                    steps,
                    handleNextStep,
                    handlePrevStep,
                    handleNextSubstep,
                    handlePrevSubstep,
                    finishExperiment,
                    submitMeasurementHorizontal,
                    handleSubmitExperiment,
                    handleCancelSubmission,
                    handleSubmissionSuccess
                };
            }
        });



        app.mount('#app');
    </script>
</body>
</html>
