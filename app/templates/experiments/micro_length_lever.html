<!-- ./app/templates/experiments/micro_length_lever.html -->
<!DOCTYPE html>
<html>
<head>
    <title>微小长度测量（光杠杆法测纸厚）实验 - 交互式实验平台</title>
    <meta name="author" content="自动生成">
    <meta name="last-modified" content="2024/10/30">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/experiment.css') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        /* Vue导航组件样式 */
        .substep-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding: 15px 0;
        }

        .substep-nav .nav-button {
            display: inline-block;
            margin: 15px 5px;
            padding: 12px 15px;
            background-color: #3498db;
            color: white !important;
            border: none;
            border-radius: 5px;
            font-size: 16px !important;
            cursor: pointer;
            transition: all 0.3s ease;
            width: auto;
            min-width: 100px;
            font-family: 'Noto Sans SC', 'Microsoft YaHei', 'SimHei', 'Segoe UI', Arial, sans-serif !important;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-weight: 400;
            line-height: 1.4;
        }

        .substep-nav .nav-button:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        }

        .substep-nav .nav-button.prev-btn {
            background-color: #95a5a6;
        }

        .substep-nav .nav-button.prev-btn:hover {
            background-color: #7f8c8d;
        }

        .validation-btn {
            background-color: #17a2b8 !important;
            color: white !important;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px !important;
            font-family: 'Noto Sans SC', 'Microsoft YaHei', 'SimHei', 'Segoe UI', Arial, sans-serif !important;
            font-weight: 400;
            line-height: 1.4;
        }

        .validation-btn:hover {
            background-color: #138496 !important;
        }

        .substep-nav .nav-button:disabled {
            background-color: #6c757d !important;
            cursor: not-allowed;
            color: white !important;
        }

        .substep-nav .nav-button:disabled:hover {
            background-color: #6c757d !important;
            transform: none;
            box-shadow: none;
        }
    </style>
</head>
<body>
    <div id="app" class="container">
        <h1>微小长度测量（光杠杆法测纸厚）实验</h1>

        <!-- 步骤1: 实验原理与器材准备 -->
        <div class="step-container active" id="step1">
            <div class="step-header">
                <div class="step-number">1</div>
                <div class="step-title">实验原理与器材准备</div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="step1-progress" style="width: 0%"></div>
            </div>
            <div class="substep-container active" id="step1-substep1">
                <div class="substep-header">
                    <div class="substep-number">1</div>
                    <div class="substep-title">实验原理</div>
                </div>
                <div class="substep-content">
                    <p>本实验利用光杠杆放大微小位移，通过测量反射光点的移动距离，结合杠杆原理，间接测量纸的厚度。</p>
                    <div class="instruction">
                        <strong>光杠杆结构：</strong>
                        <ul>
                            <li>长臂2D：2倍的平面镜到标尺的距离D</li>
                            <li>短臂b：从支点到尾部支撑的距离</li>
                            <li>D：平面镜到标尺的距离</li>
                        </ul>
                        <strong>工作原理：</strong>
                        <ul>
                            <li>当短臂尾部垫入n层纸（厚度为d）时，短臂尾部上升高度为nd</li>
                            <li>平面镜发生微小转角θ，反射光点在标尺上移动距离Δs≈2Dθ</li>
                            <li>由杠杆原理，θ≈nd/b</li>
                            <li>放大率为2D/b</li>
                            <li>因此纸厚计算公式：d = <span class="math">\frac{s \cdot b}{2 n D}</span></li>
                        </ul>
                    </div>
                    <experiment-navigation-component
                        :current-step="1"
                        :current-substep="1"
                        :total-substeps="2"
                        :show-prev="false"
                        :show-next="true"
                        next-text="下一步"
                        @next="handleSubstepNext">
                    </experiment-navigation-component>
                </div>
            </div>
            <div class="substep-container" id="step1-substep2">
                <div class="substep-header">
                    <div class="substep-number">2</div>
                    <div class="substep-title">器材准备与检查</div>
                </div>
                <div class="substep-content">
                    <ul>
                        <li>光杠杆装置</li>
                        <li>激光笔或光源</li>
                        <li>标尺</li>
                        <li>白纸若干</li>
                        <li>游标卡尺/直尺（测量b、D）</li>
                    </ul>
                    <div class="instruction">
                        <strong>检查要点：</strong>
                        <ul>
                            <li>光杠杆支架稳固，反射镜清洁</li>
                            <li>激光笔/光源电量充足</li>
                            <li>标尺刻度清晰</li>
                        </ul>
                    </div>
                    <experiment-navigation-component
                        :current-step="1"
                        :current-substep="2"
                        :total-substeps="2"
                        :show-prev="true"
                        :show-next="true"
                        prev-text="上一步"
                        next-text="进入下一个实验步骤"
                        :is-final-substep="true"
                        @prev="handleSubstepPrev"
                        @next="handleStepNext">
                    </experiment-navigation-component>
                </div>
            </div>
            <!-- 新增步骤：光杠杆调节 -->
            <div class="step-container" id="step1-5">
                <div class="step-header">
                    <div class="step-number">1.5</div>
                    <div class="step-title">光杠杆调节</div>
                </div>
                <div class="instruction">
                    <ul>
                        <li><strong>调节目标：</strong> 使反射光点在标尺上清晰、灵敏地移动，且移动范围适中。</li>
                        <li><strong>调节方法：</strong>
                            <ul>
                                <li>调整光源和反射镜的相对位置，使反射光点准确落在标尺刻度范围内。</li>
                                <li>调节b和D的长度，保证反射点移动既不过大也不过小（一般建议b较短，D适中）。</li>
                                <li>插入纸片时，观察反射点移动，确保有明显响应。</li>
                                <li>如反射点移动不明显，可适当缩短b或增加D。</li>
                            </ul>
                        </li>
                        <li><strong>注意事项：</strong>
                            <ul>
                                <li>调节过程中避免用力过大，防止装置移位。</li>
                                <li>保持反射镜和标尺表面清洁。</li>
                                <li>调节完成后，记录b和D的实际数值。</li>
                            </ul>
                        </li>
                    </ul>
                </div>
                <experiment-navigation-component
                    :current-step="1.5"
                    :show-prev="true"
                    :show-next="true"
                    prev-text="上一步"
                    next-text="进入参数测量"
                    @prev="handleStepPrev"
                    @next="handleStepNext">
                </experiment-navigation-component>
            </div>
        </div>

        <!-- 步骤2: 装置搭建与参数测量 -->
        <div class="step-container" id="step2">
            <div class="step-header">
                <div class="step-number">2</div>
                <div class="step-title">装置搭建与参数测量</div>
            </div>
            <div class="instruction">
                <ul>
                    <li>搭建光杠杆装置，调整光源使反射点落在标尺上。</li>
                    <li>用游标卡尺/直尺测量b（短臂）、D（平面镜到标尺距离），并记录。</li>
                    <li>注意：D是平面镜到标尺的距离，不是反射点到标尺的距离。</li>
                </ul>
                <strong>实验调节指导：</strong>
                <ul>
                    <li>调整光源和反射镜，使反射光点准确落在标尺刻度范围内，且移动时不会超出标尺。</li>
                    <li>确保光杠杆支架稳固，反射镜无晃动。</li>
                    <li>调节纸片插入位置，使短臂尾部能灵敏抬起，反射点有明显移动。</li>
                    <li>如反射点移动过大或过小，可适当调整D或b的长度。</li>
                </ul>
            </div>
            <div class="table-container">
                <table>
                    <thead>
                        <tr><th>参数</th><th>测量值（mm）</th></tr>
                    </thead>
                    <tbody>
                        <tr><td>b（短臂）</td><td><input type="number" step="0.01" class="param-b" required></td></tr>
                        <tr><td>D（平面镜到标尺距离）</td><td><input type="number" step="0.01" class="param-D" required></td></tr>
                    </tbody>
                </table>
            </div>
            <experiment-navigation-component
                :current-step="2"
                :show-prev="true"
                :show-next="true"
                :is-step-start="true"
                prev-text="上一步"
                next-text="下一步"
                @prev="handleStepPrev"
                @next="handleStepNext">
            </experiment-navigation-component>
        </div>

        <!-- 步骤3: 数据采集 -->
        <div class="step-container" id="step3">
            <div class="step-header">
                <div class="step-number">3</div>
                <div class="step-title">数据采集</div>
            </div>
            <div class="instruction">
                <ul>
                    <li>每次在短臂下垫入n层纸，记录反射点移动前后的位置，计算位移s。</li>
                    <li>建议测量5组不同n值的数据。</li>
                </ul>
            </div>
            <div class="table-container">
                <table>
                    <thead>
                        <tr><th>组号</th><th>纸层数n</th><th>反射点初始位置x<sub>0</sub>（mm）</th><th>反射点末位置x<sub>1</sub>（mm）</th><th>位移s=x<sub>1</sub>-x<sub>0</sub>（mm）</th></tr>
                    </thead>
                    <tbody>
                        <tr><td>1</td><td><input type="number" class="n-value" required></td><td><input type="number" step="0.01" class="x0-value" required></td><td><input type="number" step="0.01" class="x1-value" required></td><td><input type="number" step="0.01" class="s-value" readonly></td></tr>
                        <tr><td>2</td><td><input type="number" class="n-value" required></td><td><input type="number" step="0.01" class="x0-value" required></td><td><input type="number" step="0.01" class="x1-value" required></td><td><input type="number" step="0.01" class="s-value" readonly></td></tr>
                        <tr><td>3</td><td><input type="number" class="n-value" required></td><td><input type="number" step="0.01" class="x0-value" required></td><td><input type="number" step="0.01" class="x1-value" required></td><td><input type="number" step="0.01" class="s-value" readonly></td></tr>
                        <tr><td>4</td><td><input type="number" class="n-value" required></td><td><input type="number" step="0.01" class="x0-value" required></td><td><input type="number" step="0.01" class="x1-value" required></td><td><input type="number" step="0.01" class="s-value" readonly></td></tr>
                        <tr><td>5</td><td><input type="number" class="n-value" required></td><td><input type="number" step="0.01" class="x0-value" required></td><td><input type="number" step="0.01" class="x1-value" required></td><td><input type="number" step="0.01" class="s-value" readonly></td></tr>
                    </tbody>
                </table>
            </div>
            <experiment-navigation-component
                :current-step="3"
                :show-prev="true"
                :show-next="true"
                :is-step-start="true"
                prev-text="上一步"
                next-text="下一步"
                @prev="handleStepPrev"
                @next="handleStepNext">
            </experiment-navigation-component>
        </div>

        <!-- 步骤4: 数据处理与纸厚计算 -->
        <div class="step-container" id="step4">
            <div class="step-header">
                <div class="step-number">4</div>
                <div class="step-title">数据处理与纸厚计算</div>
            </div>
            <div class="instruction">
                <ul>
                    <li>自动计算每组纸厚d，显示平均值和标准差。</li>
                    <li>可点击按钮生成数据汇总表和图形。</li>
                </ul>
            </div>
            <div class="table-container" id="result-table-container" style="display: none;">
                <h3>纸厚计算结果</h3>
                <table>
                    <thead>
                        <tr><th>组号</th><th>纸层数n</th><th>位移s（mm）</th><th>纸厚d（mm）</th></tr>
                    </thead>
                    <tbody id="result-body">
                        <!-- 由JS动态生成 -->
                    </tbody>
                </table>
                <div id="summary-result"></div>
            </div>
            <button onclick="generateMicroLengthResult()">生成结果与图形</button>
            <div id="plot-container">
                <img id="plot-image" style="display: none;">
            </div>
            <experiment-navigation-component
                :current-step="4"
                :show-prev="true"
                :show-next="true"
                :is-step-start="true"
                prev-text="上一步"
                next-text="AI分析数据"
                @prev="handleStepPrev"
                @next="handleStepNext">
            </experiment-navigation-component>
        </div>

        <!-- 步骤5: AI分析与实验提交 -->
        <div class="step-container" id="step5">
            <div class="step-header">
                <div class="step-number">5</div>
                <div class="step-title">AI分析与实验提交</div>
            </div>

            <button onclick="analyzeMicroLengthData()" style="background-color: #27ae60; margin-left: 10px;">AI 分析数据</button>
            <div id="analysis-container" class="analysis-container" style="display: none;">
                <h3>AI 分析结果</h3>
                <div id="analysis-content" class="analysis-content"></div>
            </div>
            <experiment-navigation-component
                :current-step="5"
                :show-prev="true"
                :show-next="true"
                prev-text="上一步"
                next-text="完成实验"
                @prev="handleStepPrev"
                @next="handleFinishExperiment">
            </experiment-navigation-component>
        </div>

        <div class="footer">
            © 2024 | 物理实验基础课程平台 | 网页设计：朱瑞华
        </div>
    </div>

    <!-- 提交模态框 -->
    <experiment-submission-modal
        experiment-type="micro_length_lever"
        :visible="showSubmissionModal"
        @submit="handleSubmitExperiment"
        @cancel="handleCancelSubmission"
        @success="handleSubmissionSuccess"
        ref="submissionModal">
    </experiment-submission-modal>

    <!-- 引入必要的库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/showdown/2.1.0/showdown.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/showdown-katex@0.8.0/dist/showdown-katex.min.js"></script>

</script>
    <!-- 引入Vue组件 -->
    <script src="{{ url_for('static', filename='js/vue-components.js') }}"></script>

    <!-- Vue应用和微小长度测量实验特有脚本 -->
    <script>
        // Vue应用初始化
        const { createApp } = Vue;

        const app = createApp({
            data() {
                return {
                    currentStep: 1,
                    currentSubstep: 1,
                    showSubmissionModal: false
                }
            },
            components: {
                'experiment-navigation-component': ExperimentNavigationComponent,
                'experiment-submission-modal': ExperimentSubmissionModalComponent
            },
            methods: {
                handleSubstepNext(stepId, substep, totalSubsteps) {
                    const currentSubStepElement = document.getElementById(`${stepId}-substep${substep}`);
                    const nextSubStepElement = document.getElementById(`${stepId}-substep${substep + 1}`);

                    if (currentSubStepElement && nextSubStepElement) {
                        currentSubStepElement.classList.remove('active');
                        nextSubStepElement.classList.add('active');

                        // 更新进度条
                        const progressElement = document.getElementById(`${stepId}-progress`);
                        if (progressElement) {
                            const progressPercent = (substep / totalSubsteps) * 100;
                            progressElement.style.width = `${progressPercent}%`;
                        }

                        nextSubStepElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                },
                handleSubstepPrev(stepId, substep, totalSubsteps) {
                    const currentSubStepElement = document.getElementById(`${stepId}-substep${substep}`);
                    const prevSubStepElement = document.getElementById(`${stepId}-substep${substep - 1}`);

                    if (currentSubStepElement && prevSubStepElement) {
                        currentSubStepElement.classList.remove('active');
                        prevSubStepElement.classList.add('active');

                        // 更新进度条
                        const progressElement = document.getElementById(`${stepId}-progress`);
                        if (progressElement) {
                            const progressPercent = ((substep - 2) / totalSubsteps) * 100;
                            progressElement.style.width = `${Math.max(0, progressPercent)}%`;
                        }

                        prevSubStepElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                },
                handleStepNext(step) {
                    const currentStepNum = typeof step === 'number' ? step : this.currentStep;
                    const currentStepElement = document.getElementById(`step${currentStepNum}`);
                    const nextStepElement = document.getElementById(`step${currentStepNum + 1}`);

                    if (currentStepElement && nextStepElement) {
                        currentStepElement.classList.remove('active');
                        nextStepElement.classList.add('active');
                        this.currentStep = currentStepNum + 1;

                        nextStepElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                },
                handleStepPrev(step) {
                    const currentStepNum = typeof step === 'number' ? step : this.currentStep;
                    const currentStepElement = document.getElementById(`step${currentStepNum}`);
                    const prevStepElement = document.getElementById(`step${currentStepNum - 1}`);

                    if (currentStepElement && prevStepElement) {
                        currentStepElement.classList.remove('active');
                        prevStepElement.classList.add('active');
                        this.currentStep = currentStepNum - 1;

                        prevStepElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                },
                handleFinishExperiment() {
                    this.showSubmissionModal = true;
                },
                handleSubmitExperiment(submissionData) {
                    // 获取实验数据
                    const nInputs = document.getElementsByClassName('n-value');
                    const x0Inputs = document.getElementsByClassName('x0-value');
                    const x1Inputs = document.getElementsByClassName('x1-value');
                    const b = parseFloat(document.querySelector('.param-b').value);
                    const D = parseFloat(document.querySelector('.param-D').value);
                    let n_list = [], s_list = [], d_list = [], valid = true;

                    for (let i = 0; i < nInputs.length; i++) {
                        const n = parseFloat(nInputs[i].value);
                        const x0 = parseFloat(x0Inputs[i].value);
                        const x1 = parseFloat(x1Inputs[i].value);
                        if (isNaN(n) || isNaN(x0) || isNaN(x1) || n <= 0) {
                            valid = false;
                            break;
                        }
                        n_list.push(n);
                        s_list.push(x1 - x0);
                        d_list.push(s_list[s_list.length-1] * b / (2 * n * D));
                    }

                    if (!valid || isNaN(b) || isNaN(D)) {
                        this.$refs.submissionModal.showError('请填写完整且有效的数据！');
                        return;
                    }

                    // 获取已生成的图片数据
                    const plotImage = document.getElementById('plot-image');
                    let plotData = null;
                    if (plotImage && plotImage.src && plotImage.style.display !== 'none') {
                        if (plotImage.src.startsWith('data:image/png;base64,')) {
                            plotData = plotImage.src.split(',')[1];
                        }
                    }

                    // 获取AI分析结果
                    const analysisContent = document.getElementById('analysis-content');
                    let analysisResult = null;
                    if (analysisContent && analysisContent.innerHTML &&
                        document.getElementById('analysis-container').style.display !== 'none') {
                        analysisResult = analysisContent.innerText || analysisContent.textContent;
                    }

                    // 准备提交数据
                    const experimentData = {
                        experiment_type: 'micro_length_lever',
                        student_id: submissionData.studentId,
                        student_name: submissionData.studentName,
                        n_list: n_list,
                        s_list: s_list,
                        b: b,
                        D: D,
                        d_list: d_list,
                        plot_data: plotData,
                        analysis_result: analysisResult,
                        submit_time: new Date().toISOString()
                    };

                    // 使用通用提交处理函数
                    handleExperimentSubmission('micro_length_lever', experimentData,
                        // 成功回调
                        (result) => {
                            this.$refs.submissionModal.showSuccess(`实验数据提交成功！记录ID: ${result.record_id}`);
                        },
                        // 错误回调
                        (error) => {
                            this.$refs.submissionModal.showError(`提交失败: ${error}`);
                        }
                    );
                },
                handleCancelSubmission() {
                    this.showSubmissionModal = false;
                },
                handleSubmissionSuccess() {
                    this.showSubmissionModal = false;
                }
            }
        });

        app.mount('#app');

        // 页面加载完成后渲染数学公式
        document.addEventListener('DOMContentLoaded', function() {
            // 使用KaTeX自动渲染所有数学公式
            if (typeof katex !== 'undefined') {
                const mathElements = document.querySelectorAll('.math');
                mathElements.forEach(element => {
                    try {
                        katex.render(element.textContent, element, {
                            throwOnError: false,
                            displayMode: false
                        });
                    } catch (e) {
                        console.error('KaTeX rendering error:', e);
                    }
                });
            }
        });

        // AI分析数据
        function analyzeMicroLengthData() {
            // 获取实验数据
            const nInputs = document.getElementsByClassName('n-value');
            const x0Inputs = document.getElementsByClassName('x0-value');
            const x1Inputs = document.getElementsByClassName('x1-value');
            const b = parseFloat(document.querySelector('.param-b').value);
            const D = parseFloat(document.querySelector('.param-D').value);

            let n_list = [], s_list = [], d_list = [], valid = true;
            for (let i = 0; i < nInputs.length; i++) {
                const n = parseFloat(nInputs[i].value);
                const x0 = parseFloat(x0Inputs[i].value);
                const x1 = parseFloat(x1Inputs[i].value);
                if (isNaN(n) || isNaN(x0) || isNaN(x1) || n <= 0) {
                    valid = false;
                    break;
                }
                n_list.push(n);
                s_list.push(x1 - x0);
                d_list.push(s_list[s_list.length-1] * b / (2 * n * D));
            }

            if (!valid || isNaN(b) || isNaN(D)) {
                alert('请确保所有数据都已填写且有效！');
                return;
            }

            // 使用默认模型
            const selectedModelId = 'default';

            // 显示分析容器和加载状态
            const analysisContainer = document.getElementById('analysis-container');
            const analysisContent = document.getElementById('analysis-content');
            analysisContainer.style.display = 'block';
            analysisContent.innerHTML = '<p>AI正在分析数据，请稍候...</p>';

            // 滚动到分析容器
            analysisContainer.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            // 使用通用的AI分析函数
            analyzeWithAI('/micro_length_lever/analyze', {
                n_list: n_list,
                s_list: s_list,
                d_list: d_list,
                b: b,
                D: D
            }, selectedModelId,
                // 成功回调
                (renderedHTML) => {
                    displayAnalysisResult('analysis-container', 'analysis-content', renderedHTML);
                },
                // 错误回调
                (error) => {
                    displayAnalysisResult('analysis-container', 'analysis-content',
                        `<p style="color: #e74c3c;">连接服务器时发生错误，请重试！</p>`);
                }
            );
        }


    </script>
</body>
</html> 