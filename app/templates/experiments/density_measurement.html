<!DOCTYPE html>
<html>
<head>
    <title>密度测量实验 - 交互式实验平台</title>
    <meta name="author" content="自动生成">
    <meta name="last-modified" content="2024/10/30">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/experiment.css') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
    <!-- 添加中文字体支持 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;600&display=swap" rel="stylesheet">
    <!-- Vue.js CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

    </style>
</head>
<body>
    <div id="app" class="container">
        <h1>密度测量实验</h1>
        <!-- 步骤1: 实验原理 -->
        <div class="step-container active" id="step1">
            <div class="step-header">
                <div class="step-number">1</div>
                <div class="step-title">实验原理</div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="step1-progress" style="width: 0%"></div>
            </div>
            <div class="substep-container active" id="step1-substep1">
                <div class="substep-header">
                    <div class="substep-number">1</div>
                    <div class="substep-title">密度的定义与测量方法</div>
                </div>
                <div class="substep-content">
                    <div class="instruction">
                        <h3>密度的定义</h3>
                        <p>密度 \( \rho = \frac{m}{V} \)，其中 \(m\) 为质量，\(V\) 为体积。</p>
                        <h4>常用测量方法：</h4>
                        <ul>
                            <li>固体密度：用天平测质量、游标卡尺/螺旋测微计测体积</li>
                            <li>液体密度：用量筒/比重计等</li>
                        </ul>
                    </div>
                    <navigation-component
                        :current-step="1"
                        :current-substep="1"
                        :total-substeps="2"
                        :show-prev="false"
                        :show-next="true"
                        next-text="下一步"
                        @next="nextSubStep">
                    </navigation-component>
                </div>
            </div>
            <div class="substep-container" id="step1-substep2">
                <div class="substep-header">
                    <div class="substep-number">2</div>
                    <div class="substep-title">实验仪器与注意事项</div>
                </div>
                <div class="substep-content">
                    <div class="instruction">
                        <ul>
                            <li>天平：测量质量，注意调零和读数精度</li>
                            <li>游标卡尺/螺旋测微计：测量尺寸，计算体积</li>
                            <li>量筒：测量液体体积</li>
                        </ul>
                        <p>注意：实验前应校准仪器，测量时多次取平均，注意读数视线垂直刻度。</p>
                    </div>
                    <navigation-component
                        :current-step="1"
                        :current-substep="2"
                        :total-substeps="2"
                        :show-prev="true"
                        :show-next="true"
                        prev-text="上一步"
                        next-text="开始测量"
                        :is-final-substep="true"
                        @next="nextStep"
                        @prev="prevSubStep">
                    </navigation-component>
                </div>
            </div>
        </div>
        <!-- 步骤2: 数据测量与记录 -->
        <div class="step-container" id="step2">
            <div class="step-header">
                <div class="step-number">2</div>
                <div class="step-title">数据测量与记录</div>
            </div>

            <!-- Part 1: Metal Cylinder -->
            <div class="substep-container active" id="step2-part1">
                <h4>1. 金属圆柱的密度测定</h4>
                <p>分别测量圆柱体的质量，圆柱体的外径，圆柱体的高度，每个测五次。</p>
                <div class="measurement-table">
                    <table>
                        <thead>
                            <tr>
                                <th>物理量</th>
                                <th>第1次</th>
                                <th>第2次</th>
                                <th>第3次</th>
                                <th>第4次</th>
                                <th>第5次</th>
                                <th>平均值</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>圆柱体的质量 m (g)</td>
                                <td><input type="number" step="0.01" class="mass-input"></td>
                                <td><input type="number" step="0.01" class="mass-input"></td>
                                <td><input type="number" step="0.01" class="mass-input"></td>
                                <td><input type="number" step="0.01" class="mass-input"></td>
                                <td><input type="number" step="0.01" class="mass-input"></td>
                                <td id="mass-avg"></td>
                            </tr>
                            <tr>
                                <td>圆柱体的外径 d (mm)</td>
                                <td><input type="number" step="0.01" class="diameter-input"></td>
                                <td><input type="number" step="0.01" class="diameter-input"></td>
                                <td><input type="number" step="0.01" class="diameter-input"></td>
                                <td><input type="number" step="0.01" class="diameter-input"></td>
                                <td><input type="number" step="0.01" class="diameter-input"></td>
                                <td id="diameter-avg"></td>
                            </tr>
                            <tr>
                                <td>圆柱体的高度 h (mm)</td>
                                <td><input type="number" step="0.01" class="height-input"></td>
                                <td><input type="number" step="0.01" class="height-input"></td>
                                <td><input type="number" step="0.01" class="height-input"></td>
                                <td><input type="number" step="0.01" class="height-input"></td>
                                <td><input type="number" step="0.01" class="height-input"></td>
                                <td id="height-avg"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Part 2: Hydrostatic Weighing -->
            <div class="substep-container" id="step2-part2">
                <h4>2. 流体静力称衡法测量物体的密度</h4>
                <div class="measurement-table">
                    <table>
                        <thead>
                            <tr>
                                <th>物理量</th>
                                <th>第1次</th>
                                <th>第2次</th>
                                <th>第3次</th>
                                <th>第4次</th>
                                <th>第5次</th>
                                <th>平均值</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>物体在空气中的质量 m₁ (g)</td>
                                <td><input type="number" step="0.01" class="m1-input"></td>
                                <td><input type="number" step="0.01" class="m1-input"></td>
                                <td><input type="number" step="0.01" class="m1-input"></td>
                                <td><input type="number" step="0.01" class="m1-input"></td>
                                <td><input type="number" step="0.01" class="m1-input"></td>
                                <td id="m1-avg"></td>
                            </tr>
                            <tr>
                                <td>物体在水中的质量 m₂ (g)</td>
                                <td><input type="number" step="0.01" class="m2-input"></td>
                                <td><input type="number" step="0.01" class="m2-input"></td>
                                <td><input type="number" step="0.01" class="m2-input"></td>
                                <td><input type="number" step="0.01" class="m2-input"></td>
                                <td><input type="number" step="0.01" class="m2-input"></td>
                                <td id="m2-avg"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <navigation-component
                :current-step="2"
                :current-substep="1"
                :total-substeps="1"
                :show-prev="true"
                :show-next="true"
                :is-step-start="true"
                prev-text="上一步"
                next-text="计算并进入下一步"
                @next="calculateAndProceed"
                @prev="prevStep">
            </navigation-component>
        </div>
        <!-- 步骤3: 密度计算与不确定度分析 -->
        <div class="step-container" id="step3">
            <div class="step-header">
                <div class="step-number">3</div>
                <div class="step-title">密度计算与不确定度分析</div>
            </div>
            
            <div id="step3-part1" style="display:none;">
                <h4>1. 金属圆柱密度计算</h4>
                <div class="uncertainty-container">
                    <div class="formula-box">
                        <p>体积 \( V = \frac{\pi}{4} \overline{d}^2 \overline{h} \)</p>
                        <p>密度 \( \rho_1 = \frac{\overline{m}}{V} \)</p>
                        <p>相对不确定度：\( \frac{u_{\rho_1}}{\rho_1} = \sqrt{(\frac{u_m}{\overline{m}})^2 + (2\frac{u_d}{\overline{d}})^2 + (\frac{u_h}{\overline{h}})^2} \)</p>
                    </div>
                    <div class="student-input">
                        <p>你的平均值 (来自步骤2):</p>
                        <p>质量 \( \overline{m} \): <span id="avg-m-display"></span> g</p>
                        <p>直径 \( \overline{d} \): <span id="avg-d-display"></span> mm</p>
                        <p>高度 \( \overline{h} \): <span id="avg-h-display"></span> mm</p>
                        <label>体积 V (cm³)：</label>
                        <input type="number" step="0.01" id="student-volume" placeholder="输入计算结果"><br>
                        <label>密度 \( \rho_1 \) (g/cm³)：</label>
                        <input type="number" step="0.01" id="student-density-cylinder" placeholder="输入计算结果"><br>
                        <!-- For simplicity, we are not asking for uncertainty calculation from student now -->
                        <button onclick="checkCylinderDensity()">检查计算</button>
                    </div>
                </div>
            </div>

            <div id="step3-part2" style="display:none;">
                <h4>2. 流体静力称衡法密度计算</h4>
                <div class="uncertainty-container">
                    <div class="formula-box">
                        <p>密度 \( \rho_2 = \frac{\overline{m_1}}{\overline{m_1} - \overline{m_2}} \rho_{水} \)</p>
                        <p>(取 \( \rho_{水} \approx 1.00 \) g/cm³)</p>
                    </div>
                    <div class="student-input">
                        <p>你的平均值 (来自步骤2):</p>
                        <p>空气中质量 \( \overline{m_1} \): <span id="avg-m1-display"></span> g</p>
                        <p>水中质量 \( \overline{m_2} \): <span id="avg-m2-display"></span> g</p>
                        <label>密度 \( \rho_2 \) (g/cm³)：</label>
                        <input type="number" step="0.01" id="student-density-hydro" placeholder="输入计算结果"><br>
                        <button onclick="checkHydrostaticDensity()">检查计算</button>
                    </div>
                </div>
            </div>

            <navigation-component
                :current-step="3"
                :current-substep="1"
                :total-substeps="1"
                :show-prev="true"
                :show-next="true"
                :is-step-start="true"
                prev-text="上一步"
                next-text="下一步"
                :has-validation="true"
                validation-function="checkHydrostaticDensity"
                next-button-id="next-btn-3"
                @next="nextStep"
                @prev="prevStep"
                @validate="validateData">
            </navigation-component>
        </div>
        <!-- 步骤4: 数据分析与结果 -->
        <div class="step-container" id="step4">
            <div class="step-header">
                <div class="step-number">4</div>
                <div class="step-title">数据分析与结果</div>
            </div>
            <h3>实验数据与计算结果汇总</h3>
            <div class="table-container" id="summary-table-container">
                <table id="summary-table">
                    <thead>
                        <tr>
                            <th>项目</th>
                            <th>数值</th>
                        </tr>
                    </thead>
                    <tbody id="summary-table-body">
                        <!-- 由JS动态生成 -->
                    </tbody>
                </table>
            </div>
            <div class="btn-container">
                <button onclick="generateSummaryTable()">生成数据汇总表</button>
                <button onclick="analyzeDensityData()" style="background-color: #27ae60; margin-left: 10px;">AI 分析数据</button>
            </div>
            <div id="density-analysis-container" style="display:none; margin-top:20px;">
                <h4>AI分析结果</h4>
                <div id="density-analysis-content"></div>
            </div>
            <navigation-component
                :current-step="4"
                :current-substep="1"
                :total-substeps="1"
                :show-prev="true"
                :show-next="true"
                :is-step-start="true"
                prev-text="上一步"
                next-text="提交实验结果"
                @next="showSubmitModal"
                @prev="prevStep">
            </navigation-component>
        </div>
        <!-- 提交模态框 -->
        <experiment-submission-modal
            experiment-type="density_measurement"
            modal-id="density-submission-modal"
            :visible="showSubmissionModal"
            @submit="handleSubmitExperiment"
            @cancel="handleCancelSubmission"
            @success="handleSubmissionSuccess"
            ref="submissionModal">
        </experiment-submission-modal>
        <div class="footer">
            © 2024 | 物理实验基础课程平台 | 网页设计：自动生成
        </div>
    </div>
    <!-- 引入必要的库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjs/11.8.0/math.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/showdown/2.1.0/showdown.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/showdown-katex@0.8.0/dist/showdown-katex.min.js"></script>
</script>
    <script src="{{ url_for('static', filename='js/vue-components.js') }}"></script>
    <script>
        const { createApp } = Vue;

        // Vue 应用程序
        const app = createApp({
            components: {
                'navigation-component': ExperimentNavigationComponent,
                'experiment-submission-modal': ExperimentSubmissionModalComponent
            },
            data() {
                return {
                    showSubmissionModal: false
                };
            },
            methods: {
                nextSubStep(stepId, currentSubstep, totalSubsteps) {
                    if (typeof stepId === 'number') {
                        // 处理步骤间导航
                        nextStep(stepId);
                    } else {
                        // 处理子步骤导航
                        window.nextSubStep(stepId, currentSubstep, totalSubsteps);
                    }
                },
                prevSubStep(stepId, currentSubstep, totalSubsteps) {
                    if (typeof stepId === 'number') {
                        // 处理步骤间导航
                        prevStep(stepId);
                    } else {
                        // 处理子步骤导航
                        window.prevSubStep(stepId, currentSubstep, totalSubsteps);
                    }
                },
                nextStep(step) {
                    window.nextStep(step);
                },
                prevStep(step) {
                    window.prevStep(step);
                },
                calculateAndProceed() {
                    window.calculateAndProceed();
                },
                showSubmitModal() {
                    this.showSubmissionModal = true;
                },
                handleSubmitExperiment(submissionData) {
                    // 收集实验数据
                    const experimentData = this.collectExperimentData();

                    // 调用提交函数
                    this.submitExperiment(submissionData.studentId, submissionData.studentName, experimentData);
                },
                handleCancelSubmission() {
                    this.showSubmissionModal = false;
                },
                handleSubmissionSuccess() {
                    this.showSubmissionModal = false;
                    alert('实验已成功完成并提交！');
                },
                collectExperimentData() {
                    // 收集所有实验数据
                    return {
                        experimentData: window.experimentData || {}
                    };
                },
                async submitExperiment(studentId, studentName, experimentData) {
                    try {
                        // 显示提交状态
                        this.$refs.submissionModal.showStatus('正在提交实验数据，请稍候...', '#e8f4fc', '#333');

                        // 模拟提交过程
                        await new Promise(resolve => setTimeout(resolve, 1500));

                        // 显示成功状态
                        this.$refs.submissionModal.showSuccess('恭喜！实验数据已成功提交。');

                    } catch (error) {
                        // 显示错误状态
                        this.$refs.submissionModal.showError('提交失败：' + error.message);
                    }
                },
                validateData(validationFunction) {
                    if (window[validationFunction]) {
                        window[validationFunction]();
                    }
                }
            }
        });

        app.mount('#app');
    </script>
    <script>
        // Store calculated data globally for simplicity
        window.experimentData = {
            mass: [],
            diameter: [],
            height: [],
            m1: [],
            m2: [],
            avg: {},
            calcs: {},
            flags: {
                cylinderDataComplete: false,
                hydrostaticDataComplete: false
            }
        };

        document.addEventListener('DOMContentLoaded', () => {
            // Add listeners to input fields to auto-calculate averages
            addAverageListener('mass-input', 'mass-avg');
            addAverageListener('diameter-input', 'diameter-avg');
            addAverageListener('height-input', 'height-avg');
            addAverageListener('m1-input', 'm1-avg');
            addAverageListener('m2-input', 'm2-avg');
        });

        function addAverageListener(inputClass, avgId) {
            const inputs = document.querySelectorAll(`.${inputClass}`);
            inputs.forEach(input => {
                input.addEventListener('input', () => {
                    const values = Array.from(inputs).map(i => parseFloat(i.value)).filter(v => !isNaN(v));
                    if (values.length > 0) {
                        const sum = values.reduce((a, b) => a + b, 0);
                        const avg = sum / values.length;
                        document.getElementById(avgId).textContent = avg.toFixed(3);
                    } else {
                        document.getElementById(avgId).textContent = '';
                    }
                });
            });
        }
        
        function calculateAndProceed() {
            // Collect all data from tables
            const massInputs = Array.from(document.querySelectorAll('.mass-input'));
            const diameterInputs = Array.from(document.querySelectorAll('.diameter-input'));
            const heightInputs = Array.from(document.querySelectorAll('.height-input'));
            const m1Inputs = Array.from(document.querySelectorAll('.m1-input'));
            const m2Inputs = Array.from(document.querySelectorAll('.m2-input'));

            window.experimentData.mass = massInputs.map(i => parseFloat(i.value));
            window.experimentData.diameter = diameterInputs.map(i => parseFloat(i.value));
            window.experimentData.height = heightInputs.map(i => parseFloat(i.value));
            window.experimentData.m1 = m1Inputs.map(i => parseFloat(i.value));
            window.experimentData.m2 = m2Inputs.map(i => parseFloat(i.value));

            // Validation
            const cylinderDataComplete = !window.experimentData.mass.some(isNaN) && !window.experimentData.diameter.some(isNaN) && !window.experimentData.height.some(isNaN);
            const hydrostaticDataComplete = !window.experimentData.m1.some(isNaN) && !window.experimentData.m2.some(isNaN);
            
            window.experimentData.flags.cylinderDataComplete = cylinderDataComplete;
            window.experimentData.flags.hydrostaticDataComplete = hydrostaticDataComplete;

            if (!cylinderDataComplete && !hydrostaticDataComplete) {
                alert('请至少完整填写一个实验的数据！');
                return;
            }

            // Calculate averages and display sections in Step 3
            const avg = (arr) => arr.reduce((a, b) => a + b, 0) / arr.length;
            
            if (cylinderDataComplete) {
                window.experimentData.avg.mass = avg(window.experimentData.mass);
                window.experimentData.avg.diameter = avg(window.experimentData.diameter);
                window.experimentData.avg.height = avg(window.experimentData.height);
                document.getElementById('avg-m-display').textContent = window.experimentData.avg.mass.toFixed(3);
                document.getElementById('avg-d-display').textContent = window.experimentData.avg.diameter.toFixed(3);
                document.getElementById('avg-h-display').textContent = window.experimentData.avg.height.toFixed(3);
                document.getElementById('step3-part1').style.display = 'block';
            } else {
                document.getElementById('step3-part1').style.display = 'none';
            }

            if (hydrostaticDataComplete) {
                window.experimentData.avg.m1 = avg(window.experimentData.m1);
                window.experimentData.avg.m2 = avg(window.experimentData.m2);
                document.getElementById('avg-m1-display').textContent = window.experimentData.avg.m1.toFixed(3);
                document.getElementById('avg-m2-display').textContent = window.experimentData.avg.m2.toFixed(3);
                document.getElementById('step3-part2').style.display = 'block';
            } else {
                document.getElementById('step3-part2').style.display = 'none';
            }

            // Reset calculation checks for step 3 and disable next button
            cylinderCalcCorrect = false;
            hydroCalcCorrect = false;
            updateNextButtonState();
            
            nextStep(2);
        }

        let cylinderCalcCorrect = false;
        let hydroCalcCorrect = false;

        function checkCylinderDensity() {
            const avg_d_cm = window.experimentData.avg.diameter / 10; // mm to cm
            const avg_h_cm = window.experimentData.avg.height / 10; // mm to cm
            const correctVolume = (Math.PI / 4) * Math.pow(avg_d_cm, 2) * avg_h_cm;
            const correctDensity = window.experimentData.avg.mass / correctVolume;
            window.experimentData.calcs.volume = correctVolume;
            window.experimentData.calcs.density_cylinder = correctDensity;

            const studentVolume = parseFloat(document.getElementById('student-volume').value);
            const studentDensity = parseFloat(document.getElementById('student-density-cylinder').value);

            if (Math.abs(studentVolume - correctVolume) / correctVolume < 0.05 && Math.abs(studentDensity - correctDensity) / correctDensity < 0.05) {
                alert('金属圆柱密度计算正确！');
                cylinderCalcCorrect = true;
            } else {
                alert(`计算有误。正确参考值：体积 ≈ ${correctVolume.toFixed(3)} cm³, 密度 ≈ ${correctDensity.toFixed(3)} g/cm³。请检查计算过程。`);
                cylinderCalcCorrect = false;
            }
            updateNextButtonState();
        }

        function checkHydrostaticDensity() {
            const correctDensity = window.experimentData.avg.m1 / (window.experimentData.avg.m1 - window.experimentData.avg.m2); // Assuming rho_water = 1
            window.experimentData.calcs.density_hydro = correctDensity;
            const studentDensity = parseFloat(document.getElementById('student-density-hydro').value);
            
            if (Math.abs(studentDensity - correctDensity) / correctDensity < 0.05) {
                alert('流体静力称衡法密度计算正确！');
                hydroCalcCorrect = true;
            } else {
                alert(`计算有误。正确参考值：密度 ≈ ${correctDensity.toFixed(3)} g/cm³。请检查计算过程。`);
                hydroCalcCorrect = false;
            }
            updateNextButtonState();
        }
        
        function updateNextButtonState() {
            const { cylinderDataComplete, hydrostaticDataComplete } = window.experimentData.flags;

            const cylinderOk = !cylinderDataComplete || cylinderCalcCorrect;
            const hydroOk = !hydrostaticDataComplete || hydroCalcCorrect;

            if (cylinderOk && hydroOk) {
                document.getElementById('next-btn-3').disabled = false;
            } else {
                document.getElementById('next-btn-3').disabled = true;
            }
        }

        function generateSummaryTable() {
            const tableBody = document.getElementById('summary-table-body');
            tableBody.innerHTML = ''; // Clear existing rows

            const { avg, flags, calcs } = window.experimentData;

            function addRow(item, value, unit = '') {
                const row = document.createElement('tr');
                const itemCell = document.createElement('td');
                const valueCell = document.createElement('td');
                itemCell.textContent = item;
                const numericValue = parseFloat(value);
                valueCell.textContent = (value !== undefined && !isNaN(numericValue)) ? `${numericValue.toFixed(3)} ${unit}`.trim() : 'N/A';
                row.appendChild(itemCell);
                row.appendChild(valueCell);
                tableBody.appendChild(row);
            }
            
            if (flags.cylinderDataComplete) {
                addRow('金属圆柱 - 平均质量', avg.mass, 'g');
                addRow('金属圆柱 - 平均直径', avg.diameter, 'mm');
                addRow('金属圆柱 - 平均高度', avg.height, 'mm');
                if(calcs.volume) addRow('金属圆柱 - 计算体积', calcs.volume, 'cm³');
                if(calcs.density_cylinder) addRow('金属圆柱 - 计算密度', calcs.density_cylinder, 'g/cm³');
            }

            if (flags.hydrostaticDataComplete) {
                addRow('流体静力称衡法 - 平均空气中质量', avg.m1, 'g');
                addRow('流体静力称衡法 - 平均水中质量', avg.m2, 'g');
                if(calcs.density_hydro) addRow('流体静力称衡法 - 计算密度', calcs.density_hydro, 'g/cm³');
            }

            if (!flags.cylinderDataComplete && !flags.hydrostaticDataComplete) {
                addRow('没有可汇总的数据', '请先完成数据测量步骤');
            }
        }

        function analyzeDensityData() {
            const analysisContainer = document.getElementById('density-analysis-container');
            const analysisContent = document.getElementById('density-analysis-content');
            analysisContainer.style.display = 'block';
            analysisContent.innerHTML = '<p>正在分析数据...</p>';

            const { flags, calcs } = window.experimentData;
            
            if ((!flags.cylinderDataComplete || !calcs.density_cylinder) && (!flags.hydrostaticDataComplete || !calcs.density_hydro)) {
                 analysisContent.innerHTML = '没有足够的数据进行分析。请先完成测量和计算。';
                 return;
            }
            
            let report = '### 实验数据分析\n\n';

            if (flags.cylinderDataComplete && calcs.density_cylinder) {
                report += `通过直接测量法，计算得到的金属圆柱密度为 **${calcs.density_cylinder.toFixed(3)} g/cm³**。\n\n`;
            }
            if (flags.hydrostaticDataComplete && calcs.density_hydro) {
                report += `通过流体静力称衡法，计算得到的物体密度为 **${calcs.density_hydro.toFixed(3)} g/cm³**。\n\n`;
            }

            if (flags.cylinderDataComplete && calcs.density_cylinder && flags.hydrostaticDataComplete && calcs.density_hydro) {
                const diff = Math.abs(calcs.density_cylinder - calcs.density_hydro);
                const avg_density = (calcs.density_cylinder + calcs.density_hydro) / 2;
                const relative_diff = (diff / avg_density) * 100;
                report += `两种方法测得的密度值相差 ${diff.toFixed(3)} g/cm³，相对差异为 ${relative_diff.toFixed(2)}%。这个差异可能来源于测量误差、仪器精度限制或操作不当。`;
            } else {
                report += '只完成了一种测量方法，无法进行比较分析。';
            }
            
            const converter = new showdown.Converter();
            analysisContent.innerHTML = converter.makeHtml(report);
        }

        function showSubmitModal() {
            document.getElementById('density-submission-modal').style.display = 'flex';
        }

        function closeDensitySubmitModal() {
            document.getElementById('density-submission-modal').style.display = 'none';
        }
        
        async function submitDensityExperiment() {
            const studentId = document.getElementById('density-student-id').value;
            const studentName = document.getElementById('density-student-name').value;
            const statusDiv = document.getElementById('density-submission-status');

            if (!studentId || !studentName) {
                statusDiv.textContent = '学号和姓名不能为空！';
                statusDiv.style.color = 'red';
                statusDiv.style.display = 'block';
                return;
            }

            statusDiv.innerHTML = '正在提交...';
            statusDiv.style.color = 'blue';
            statusDiv.style.display = 'block';
            
            console.log('Submitting data:', {
                studentId,
                studentName,
                data: window.experimentData
            });

            await new Promise(resolve => setTimeout(resolve, 1500));
            
            statusDiv.innerHTML = '实验数据提交成功！';
            statusDiv.style.color = 'green';

            setTimeout(() => {
                closeDensitySubmitModal();
                statusDiv.style.display = 'none';
            }, 2000);
        }
    </script>
</body>
</html>
