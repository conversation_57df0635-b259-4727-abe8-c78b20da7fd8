import numpy as np
import pytest

from numpy.testing import assert_allclose, assert_equal

from scipy.special._ufuncs import _log1mexp

# # Test cases generated with the script
#
# import numpy as np

# from mpmath import mp


# def mp_log1mexp(x):
#     with mp.workdps(324):
#         return float(mp.log(mp.one - mp.exp(x)))

# X = np.concat([-np.logspace(-1, -300, 20), np.linspace(-745, -1, 20)])

# cases = [(float(x), mp_log1mexp(x)) for x in X]

@pytest.mark.parametrize(
    "x,expected",
    [
        (-0.1, -2.3521684610440907),
        (-1.8329807108324374e-17, -38.538003135374026),
        (-3.359818286283788e-33, -74.773421177754),
        (-6.1584821106602796e-49, -111.00883922013399),
        (-1.1288378916846929e-64, -147.24425726251397),
        (-2.0691380811148324e-80, -183.47967530489393),
        (-3.792690190732269e-96, -219.71509334727392),
        (-6.951927961775534e-112, -255.95051138965394),
        (-1.2742749857031425e-127, -292.1859294320339),
        (-2.3357214690901785e-143, -328.42134747441384),
        (-4.281332398719571e-159, -364.6567655167938),
        (-7.847599703514559e-175, -400.8921835591739),
        (-1.4384498882876776e-190, -437.1276016015538),
        (-2.6366508987304307e-206, -473.3630196439338),
        (-4.832930238571653e-222, -509.59843768631384),
        (-8.858667904100796e-238, -545.8338557286938),
        (-1.623776739188744e-253, -582.0692737710738),
        (-2.9763514416312156e-269, -618.3046918134538),
        (-5.455594781168782e-285, -654.5401098558336),
        (-1e-300, -690.7755278982137),
        (-745.0, -5e-324),
        (-705.8421052631579, -2.8619931451743316e-307),
        (-666.6842105263158, -2.9021923726875757e-290),
        (-627.5263157894738, -2.9429562339405562e-273),
        (-588.3684210526316, -2.9842926597143714e-256),
        (-549.2105263157895, -3.0262096921839423e-239),
        (-510.0526315789474, -3.0687154864846747e-222),
        (-470.89473684210526, -3.1118183122979086e-205),
        (-431.7368421052632, -3.155526555459449e-188),
        (-392.5789473684211, -3.1998487195921207e-171),
        (-353.42105263157896, -3.2447934277596653e-154),
        (-314.2631578947369, -3.2903694241438367e-137),
        (-275.1052631578948, -3.3365855757467166e-120),
        (-235.94736842105266, -3.3834508741152875e-103),
        (-196.78947368421052, -3.4309744370903894e-86),
        (-157.63157894736844, -3.4791655105810003e-69),
        (-118.47368421052636, -3.528033470363468e-52),
        (-79.31578947368428, -3.577587823905024e-35),
        (-40.157894736842195, -3.627838212213697e-18),
        (-1.0, -0.4586751453870819),
    ]
)
def test_log1mexp(x, expected):
    observed = _log1mexp(x)
    assert_allclose(observed, expected, rtol=1e-15)


@pytest.mark.parametrize("x", [1.1, 1e10, np.inf])
def test_log1mexp_out_of_domain(x):
    observed = _log1mexp(x)
    assert np.isnan(observed)


@pytest.mark.parametrize(
    "x,expected",
    [(-np.inf, -0.0), (0.0, -np.inf), (-0.0, -np.inf), (np.nan, np.nan)]
)
def test_log1mexp_extreme(x, expected):
    observed = _log1mexp(x)
    assert_equal(expected, observed)
