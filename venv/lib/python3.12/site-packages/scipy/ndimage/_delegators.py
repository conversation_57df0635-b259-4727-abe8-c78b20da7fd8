"""Delegators for alternative backends in scipy.ndimage.

The signature of `func_signature` must match the signature of ndimage.func.
The job of a `func_signature` is to know which arguments of `ndimage.func`
are arrays.

* signatures are generated by

--------------
import inspect
from scipy import ndimage

names = [x for x in dir(ndimage) if not x.startswith('_')]
objs = [getattr(ndimage, name) for name in names]
funcs = [obj for obj in objs if inspect.isroutine(obj)]

for func in funcs:
    sig = inspect.signature(func)
    print(f"def {func.__name__}_signature{sig}:\n\tpass\n\n")
---------------

* which arguments to delegate on: manually trawled the documentation for
  array-like and array arguments

"""
import numpy as np
from scipy._lib._array_api import array_namespace
from scipy.ndimage._ni_support import _skip_if_dtype


def affine_transform_signature(
    input, matrix, offset=0.0, output_shape=None, output=None, *args, **kwds
):
    return array_namespace(input, matrix, _skip_if_dtype(output))


def binary_closing_signature(
    input, structure=None, iterations=1, output=None, *args, **kwds
):
    return array_namespace(input, structure, _skip_if_dtype(output))

binary_opening_signature = binary_closing_signature


def binary_dilation_signature(
    input, structure=None, iterations=1, mask=None, output=None, *args, **kwds
):
    return array_namespace(input, structure, _skip_if_dtype(output), mask)

binary_erosion_signature = binary_dilation_signature


def binary_fill_holes_signature(
    input, structure=None, output=None, origin=0, *args, **kwargs
):
    return array_namespace(input, structure, _skip_if_dtype(output))


def label_signature(input, structure=None, output=None, origin=0):
    return array_namespace(input, structure, _skip_if_dtype(output))


def binary_hit_or_miss_signature(
    input, structure1=None, structure2=None, output=None, *args, **kwds
):
    return array_namespace(input, structure1, structure2, _skip_if_dtype(output))


def binary_propagation_signature(
    input, structure=None, mask=None, output=None, *args, **kwds
):
    return array_namespace(input, structure, mask, _skip_if_dtype(output))


def convolve_signature(input, weights, output=None, *args, **kwds):
    return array_namespace(input, weights, _skip_if_dtype(output))

correlate_signature = convolve_signature


def convolve1d_signature(input, weights, axis=-1, output=None, *args, **kwds):
    return array_namespace(input, weights, _skip_if_dtype(output))

correlate1d_signature = convolve1d_signature


def distance_transform_bf_signature(
    input, metric='euclidean', sampling=None, return_distances=True,
    return_indices=False, distances=None, indices=None
):
    return array_namespace(input, distances, indices)


def distance_transform_cdt_signature(
    input, metric='chessboard', return_distances=True, return_indices=False,
    distances=None, indices=None
):
    return array_namespace(input, distances, indices)


def distance_transform_edt_signature(
    input, sampling=None, return_distances=True, return_indices=False,
    distances=None, indices=None
):
    return array_namespace(input, distances, indices)


def find_objects_signature(input, max_label=0):
    return array_namespace(input)


def fourier_ellipsoid_signature(input, size, n=-1, axis=-1, output=None):
    return array_namespace(input, _skip_if_dtype(output))

fourier_uniform_signature = fourier_ellipsoid_signature


def fourier_gaussian_signature(input, sigma, n=-1, axis=-1, output=None):
    return array_namespace(input, _skip_if_dtype(output))

def fourier_shift_signature(input, shift, n=-1, axis=-1, output=None):
    return array_namespace(input, _skip_if_dtype(output))


def gaussian_filter_signature(input, sigma, order=0, output=None, *args, **kwds):
    return array_namespace(input, _skip_if_dtype(output))


def gaussian_filter1d_signature(
    input, sigma, axis=-1, order=0, output=None, *args, **kwds
):
    return array_namespace(input, _skip_if_dtype(output))


def gaussian_gradient_magnitude_signature(input, sigma, output=None, *args, **kwds):
    return array_namespace(input, _skip_if_dtype(output))

gaussian_laplace_signature = gaussian_gradient_magnitude_signature


def generate_binary_structure_signature(rank, connectivity):
    # XXX: no input arrays; always return numpy
    return np


def generic_filter_signature(
    input, function, size=None, footprint=None, output=None, *args, **kwds
):
    # XXX: function LowLevelCallable w/backends
    return array_namespace(input, footprint, _skip_if_dtype(output))


def generic_filter1d_signature(
    input, function, filter_size, axis=-1, output=None, *args, **kwds
):
    return array_namespace(input, _skip_if_dtype(output))


def generic_gradient_magnitude_signature(
    input, derivative, output=None, *args, **kwds
):
    # XXX: function LowLevelCallable w/backends
    return array_namespace(input, _skip_if_dtype(output))


def generic_laplace_signature(input, derivative2, output=None, *args, **kwds):
    # XXX: function LowLevelCallable w/backends
    return array_namespace(input, _skip_if_dtype(output))


def geometric_transform_signature(
    input, mapping, output_shape=None, output=None, *args, **kwds
):
    return array_namespace(input, _skip_if_dtype(output))


def histogram_signature(input, min, max, bins, labels=None, index=None):
    return array_namespace(input, labels)


def iterate_structure_signature(structure, iterations, origin=None):
    return array_namespace(structure)


def labeled_comprehension_signature(input, labels, *args, **kwds):
    return array_namespace(input, labels)


def laplace_signature(input, output=None, *args, **kwds):
    return array_namespace(input, _skip_if_dtype(output))


def map_coordinates_signature(input, coordinates, output=None, *args, **kwds):
    return array_namespace(input, coordinates, _skip_if_dtype(output))


def maximum_filter1d_signature(input, size, axis=-1, output=None, *args, **kwds):
    return array_namespace(input, _skip_if_dtype(output))

minimum_filter1d_signature = maximum_filter1d_signature
uniform_filter1d_signature = maximum_filter1d_signature


def maximum_signature(input, labels=None, index=None):
    return array_namespace(input, labels, index)

minimum_signature = maximum_signature
median_signature = maximum_signature
mean_signature = maximum_signature
variance_signature = maximum_signature
standard_deviation_signature = maximum_signature
sum_labels_signature = maximum_signature
sum_signature = maximum_signature  # ndimage.sum is sum_labels

maximum_position_signature = maximum_signature
minimum_position_signature = maximum_signature

extrema_signature = maximum_signature
center_of_mass_signature = extrema_signature


def median_filter_signature(
    input, size=None, footprint=None, output=None, *args, **kwds
):
    return array_namespace(input, footprint, _skip_if_dtype(output))

minimum_filter_signature = median_filter_signature
maximum_filter_signature = median_filter_signature


def morphological_gradient_signature(
    input, size=None, footprint=None, structure=None, output=None, *args, **kwds
):
    return array_namespace(input, footprint, structure, _skip_if_dtype(output))

morphological_laplace_signature = morphological_gradient_signature
white_tophat_signature = morphological_gradient_signature
black_tophat_signature = morphological_gradient_signature
grey_closing_signature = morphological_gradient_signature
grey_dilation_signature = morphological_gradient_signature
grey_erosion_signature = morphological_gradient_signature
grey_opening_signature = morphological_gradient_signature


def percentile_filter_signature(
    input, percentile, size=None, footprint=None, output=None, *args, **kwds
):
    return array_namespace(input, footprint, _skip_if_dtype(output))


def prewitt_signature(input, axis=-1, output=None, *args, **kwds):
    return array_namespace(input, _skip_if_dtype(output))

sobel_signature = prewitt_signature


def rank_filter_signature(
    input, rank, size=None, footprint=None, output=None, *args, **kwds
):
    return array_namespace(input, footprint, _skip_if_dtype(output))


def rotate_signature(
    input, angle, axes=(1, 0), reshape=True, output=None , *args, **kwds
):
    return array_namespace(input, _skip_if_dtype(output))


def shift_signature(input, shift, output=None, *args, **kwds):
    return array_namespace(input, _skip_if_dtype(output))


def spline_filter_signature(input, order=3, output=np.float64, *args, **kwds):
    return array_namespace(input, _skip_if_dtype(output))


def spline_filter1d_signature(
    input, order=3, axis=-1, output=np.float64, *args, **kwds
):
    return array_namespace(input, _skip_if_dtype(output))


def uniform_filter_signature(input, size=3, output=None, *args, **kwds):
    return array_namespace(input, _skip_if_dtype(output))


def value_indices_signature(arr, *args, **kwds):
    return array_namespace(arr)


def vectorized_filter_signature(
    input, function, size=None, footprint=None, output=None, *args, **kwds
):
    return array_namespace(input, footprint, _skip_if_dtype(output))


def watershed_ift_signature(input, markers, structure=None, output=None):
    return array_namespace(input, markers, structure, _skip_if_dtype(output))


def zoom_signature(input, zoom, output=None, *args, **kwds):
    return array_namespace(input, _skip_if_dtype(output))

