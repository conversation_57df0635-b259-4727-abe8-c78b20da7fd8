Metadata-Version: 2.1
Name: kiwisolver
Version: 1.4.8
Summary: A fast implementation of the Cassowary constraint solver
Author-email: The Nucleic Development Team <<EMAIL>>
Maintainer-email: "<PERSON>hieu C<PERSON>" <<EMAIL>>
License: =========================
         The Kiwi licensing terms
        =========================
        Ki<PERSON> is licensed under the terms of the Modified BSD License (also known as
        New or Revised BSD), as follows:
        
        Copyright (c) 2013-2024, Nucleic Development Team
        
        All rights reserved.
        
        Redistribution and use in source and binary forms, with or without
        modification, are permitted provided that the following conditions are met:
        
        Redistributions of source code must retain the above copyright notice, this
        list of conditions and the following disclaimer.
        
        Redistributions in binary form must reproduce the above copyright notice, this
        list of conditions and the following disclaimer in the documentation and/or
        other materials provided with the distribution.
        
        Neither the name of the Nucleic Development Team nor the names of its
        contributors may be used to endorse or promote products derived from this
        software without specific prior written permission.
        
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
        ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
        WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
        DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE
        FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
        DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
        SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
        OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
        OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
        
        About Kiwi
        ----------
        Chris Colbert began the Kiwi project in December 2013 in an effort to
        create a blisteringly fast UI constraint solver. Chris is still the
        project lead.
        
        The Nucleic Development Team is the set of all contributors to the Nucleic
        project and its subprojects.
        
        The core team that coordinates development on GitHub can be found here:
        http://github.com/nucleic. The current team consists of:
        
        * Chris Colbert
        
        Our Copyright Policy
        --------------------
        Nucleic uses a shared copyright model. Each contributor maintains copyright
        over their contributions to Nucleic. But, it is important to note that these
        contributions are typically only changes to the repositories. Thus, the Nucleic
        source code, in its entirety is not the copyright of any single person or
        institution. Instead, it is the collective copyright of the entire Nucleic
        Development Team. If individual contributors want to maintain a record of what
        changes/contributions they have specific copyright on, they should indicate
        their copyright in the commit message of the change, when they commit the
        change to one of the Nucleic repositories.
        
        With this in mind, the following banner should be used in any source code file
        to indicate the copyright and license terms:
        
        #------------------------------------------------------------------------------
        # Copyright (c) 2013-2024, Nucleic Development Team.
        #
        # Distributed under the terms of the Modified BSD License.
        #
        # The full license is in the file LICENSE, distributed with this software.
        #------------------------------------------------------------------------------
        
Project-URL: homepage, https://github.com/nucleic/kiwi
Project-URL: documentation, https://kiwisolver.readthedocs.io/en/latest/
Project-URL: repository, https://github.com/nucleic/kiwi
Project-URL: changelog, https://github.com/nucleic/kiwi/blob/main/releasenotes.rst
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Requires-Python: >=3.10
Description-Content-Type: text/x-rst
License-File: LICENSE

Welcome to Kiwi
===============

.. image:: https://github.com/nucleic/kiwi/workflows/Continuous%20Integration/badge.svg
    :target: https://github.com/nucleic/kiwi/actions
.. image:: https://github.com/nucleic/kiwi/workflows/Documentation%20building/badge.svg
    :target: https://github.com/nucleic/kiwi/actions
.. image:: https://codecov.io/gh/nucleic/kiwi/branch/main/graph/badge.svg
  :target: https://codecov.io/gh/nucleic/kiwi
.. image:: https://readthedocs.org/projects/kiwisolver/badge/?version=latest
    :target: https://kiwisolver.readthedocs.io/en/latest/?badge=latest
    :alt: Documentation Status

Kiwi is an efficient C++ implementation of the Cassowary constraint solving
algorithm. Kiwi is an implementation of the algorithm based on the
`seminal Cassowary paper <https://constraints.cs.washington.edu/solvers/cassowary-tochi.pdf>`_.
It is *not* a refactoring of the original C++ solver. Kiwi has been designed
from the ground up to be lightweight and fast. Kiwi ranges from 10x to 500x
faster than the original Cassowary solver with typical use cases gaining a 40x
improvement. Memory savings are consistently > 5x.

In addition to the C++ solver, Kiwi ships with hand-rolled Python bindings for
Python 3.7+.
